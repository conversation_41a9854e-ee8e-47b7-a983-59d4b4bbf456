<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.AirUserMapper">
    
    <resultMap type="AirUser" id="AirUserResult">
        <result property="userId"       column="user_id"       />
        <result property="phone"        column="phone"         />
        <result property="name"         column="name"          />
        <result property="carBrand"     column="car_brand"     />
        <result property="openid"       column="openid"        />
        <result property="avatarUrl"    column="avatar_url"    />
        <result property="gender"       column="gender"        />
        <result property="city"         column="city"          />
        <result property="province"     column="province"      />
        <result property="country"      column="country"       />
        <result property="status"       column="status"        />
        <result property="createTime"   column="create_time"   />
        <result property="updateTime"   column="update_time"   />
    </resultMap>

    <sql id="selectAirUserVo">
        select user_id, phone, name, car_brand, openid, avatar_url, gender, city, province, country, status, create_time, update_time from air_user
    </sql>

    <select id="selectAirUserList" parameterType="AirUser" resultMap="AirUserResult">
        <include refid="selectAirUserVo"/>
        <where>  
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="carBrand != null  and carBrand != ''"> and car_brand = #{carBrand}</if>
            <if test="openid != null  and openid != ''"> and openid = #{openid}</if>
            <if test="gender != null "> and gender = #{gender}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="province != null  and province != ''"> and province = #{province}</if>
            <if test="country != null  and country != ''"> and country = #{country}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectAirUserByUserId" parameterType="Long" resultMap="AirUserResult">
        <include refid="selectAirUserVo"/>
        where user_id = #{userId}
    </select>

    <select id="selectAirUserByPhone" parameterType="String" resultMap="AirUserResult">
        <include refid="selectAirUserVo"/>
        where phone = #{phone}
    </select>

    <select id="selectAirUserByOpenid" parameterType="String" resultMap="AirUserResult">
        <include refid="selectAirUserVo"/>
        where openid = #{openid}
    </select>
        
    <insert id="insertAirUser" parameterType="AirUser" useGeneratedKeys="true" keyProperty="userId">
        insert into air_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="phone != null and phone != ''">phone,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="carBrand != null and carBrand != ''">car_brand,</if>
            <if test="openid != null and openid != ''">openid,</if>
            <if test="avatarUrl != null and avatarUrl != ''">avatar_url,</if>
            <if test="gender != null">gender,</if>
            <if test="city != null and city != ''">city,</if>
            <if test="province != null and province != ''">province,</if>
            <if test="country != null and country != ''">country,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="carBrand != null and carBrand != ''">#{carBrand},</if>
            <if test="openid != null and openid != ''">#{openid},</if>
            <if test="avatarUrl != null and avatarUrl != ''">#{avatarUrl},</if>
            <if test="gender != null">#{gender},</if>
            <if test="city != null and city != ''">#{city},</if>
            <if test="province != null and province != ''">#{province},</if>
            <if test="country != null and country != ''">#{country},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateAirUser" parameterType="AirUser">
        update air_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="carBrand != null and carBrand != ''">car_brand = #{carBrand},</if>
            <if test="openid != null and openid != ''">openid = #{openid},</if>
            <if test="avatarUrl != null and avatarUrl != ''">avatar_url = #{avatarUrl},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="city != null and city != ''">city = #{city},</if>
            <if test="province != null and province != ''">province = #{province},</if>
            <if test="country != null and country != ''">country = #{country},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where user_id = #{userId}
    </update>

    <delete id="deleteAirUserByUserId" parameterType="Long">
        delete from air_user where user_id = #{userId}
    </delete>

    <delete id="deleteAirUserByUserIds" parameterType="String">
        delete from air_user where user_id in 
        <foreach item="userId" collection="array" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>
</mapper>
