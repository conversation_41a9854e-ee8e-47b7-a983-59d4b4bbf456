/**
 * 滤芯提醒服务
 * 负责滤芯使用时间跟踪、清洗提醒、更换提醒等功能
 */

const storageService = require('./storage.js');
const notificationService = require('./notification.js');

class FilterReminder {
  constructor() {
    this.filterLifeDays = 180; // 滤芯寿命180天
    this.cleanReminderDays = 30; // 每30天提醒清洗
    this.warningDays = 7; // 提前7天警告
    
    this.filterData = {
      installTime: null,
      lastCleanTime: null,
      cleanCount: 0,
      usageDays: 0,
      remainingDays: 180,
      status: 0 // 0-正常, 1-需要清洗, 2-需要更换
    };
    
    this.loadFilterData();
  }

  /**
   * 初始化滤芯（首次安装或更换时调用）
   */
  initFilter(deviceId, installTime = null) {
    this.deviceId = deviceId;
    const now = installTime || Date.now();
    
    this.filterData = {
      installTime: now,
      lastCleanTime: null,
      cleanCount: 0,
      usageDays: 0,
      remainingDays: this.filterLifeDays,
      status: 0
    };
    
    this.saveFilterData();
    
    console.log('🔧 滤芯已初始化，开始计时');
    
    // 设置提醒检查
    this.scheduleReminders();
    
    return this.getFilterStatus();
  }

  /**
   * 记录清洗操作
   */
  recordClean(deviceId) {
    if (!this.filterData.installTime) {
      console.warn('滤芯未初始化，无法记录清洗');
      return false;
    }

    this.deviceId = deviceId;
    this.filterData.lastCleanTime = Date.now();
    this.filterData.cleanCount += 1;
    
    // 清洗后重置部分状态
    if (this.filterData.status === 1) {
      this.filterData.status = 0; // 清洗后状态恢复正常
    }
    
    this.saveFilterData();
    
    console.log(`🧽 记录清洗操作，累计清洗 ${this.filterData.cleanCount} 次`);
    
    // 发送清洗完成通知
    notificationService.showNotification({
      title: '清洗完成',
      content: `滤芯清洗记录已保存，累计清洗 ${this.filterData.cleanCount} 次`,
      type: 'success'
    });
    
    return true;
  }

  /**
   * 获取滤芯状态
   */
  getFilterStatus() {
    if (!this.filterData.installTime) {
      return {
        ...this.filterData,
        needInit: true,
        statusText: '未初始化',
        statusColor: '#999'
      };
    }

    // 计算使用天数
    const now = Date.now();
    const usageDays = Math.floor((now - this.filterData.installTime) / (1000 * 60 * 60 * 24));
    const remainingDays = Math.max(0, this.filterLifeDays - usageDays);
    
    // 更新状态
    this.filterData.usageDays = usageDays;
    this.filterData.remainingDays = remainingDays;
    
    // 判断状态
    let status = 0;
    let statusText = '状态良好';
    let statusColor = '#27ae60';
    
    if (usageDays >= this.filterLifeDays) {
      status = 2; // 需要更换
      statusText = '需要更换';
      statusColor = '#e74c3c';
    } else if (remainingDays <= this.warningDays) {
      status = 2; // 即将到期
      statusText = '即将到期';
      statusColor = '#e67e22';
    } else if (this.shouldClean()) {
      status = 1; // 需要清洗
      statusText = '建议清洗';
      statusColor = '#f39c12';
    }
    
    this.filterData.status = status;
    
    return {
      ...this.filterData,
      statusText,
      statusColor,
      usagePercent: Math.min(100, (usageDays / this.filterLifeDays) * 100),
      nextCleanDays: this.getNextCleanDays(),
      needInit: false
    };
  }

  /**
   * 检查是否需要清洗
   */
  shouldClean() {
    if (!this.filterData.lastCleanTime) {
      // 如果从未清洗，检查是否超过清洗间隔
      const daysSinceInstall = Math.floor((Date.now() - this.filterData.installTime) / (1000 * 60 * 60 * 24));
      return daysSinceInstall >= this.cleanReminderDays;
    }
    
    // 检查距离上次清洗是否超过间隔
    const daysSinceClean = Math.floor((Date.now() - this.filterData.lastCleanTime) / (1000 * 60 * 60 * 24));
    return daysSinceClean >= this.cleanReminderDays;
  }

  /**
   * 获取下次清洗建议天数
   */
  getNextCleanDays() {
    const lastTime = this.filterData.lastCleanTime || this.filterData.installTime;
    if (!lastTime) return 0;
    
    const daysSinceLast = Math.floor((Date.now() - lastTime) / (1000 * 60 * 60 * 24));
    return Math.max(0, this.cleanReminderDays - daysSinceLast);
  }

  /**
   * 检查并发送提醒
   */
  checkReminders() {
    const status = this.getFilterStatus();
    
    if (status.needInit) {
      return;
    }

    // 更换提醒
    if (status.status === 2 && status.remainingDays <= 0) {
      this.sendReplaceReminder();
    }
    // 即将到期提醒
    else if (status.remainingDays <= this.warningDays && status.remainingDays > 0) {
      this.sendWarningReminder(status.remainingDays);
    }
    // 清洗提醒
    else if (status.status === 1) {
      this.sendCleanReminder();
    }
  }

  /**
   * 发送更换提醒
   */
  sendReplaceReminder() {
    notificationService.showNotification({
      title: '滤芯需要更换',
      content: `滤芯已使用 ${this.filterData.usageDays} 天，建议立即更换新滤芯`,
      type: 'error',
      duration: 0 // 不自动消失
    });
  }

  /**
   * 发送警告提醒
   */
  sendWarningReminder(remainingDays) {
    notificationService.showNotification({
      title: '滤芯即将到期',
      content: `滤芯还有 ${remainingDays} 天到期，请准备更换`,
      type: 'warning'
    });
  }

  /**
   * 发送清洗提醒
   */
  sendCleanReminder() {
    const daysSinceClean = this.filterData.lastCleanTime ? 
      Math.floor((Date.now() - this.filterData.lastCleanTime) / (1000 * 60 * 60 * 24)) :
      Math.floor((Date.now() - this.filterData.installTime) / (1000 * 60 * 60 * 24));
      
    notificationService.showNotification({
      title: '建议清洗滤芯',
      content: `滤芯已使用 ${daysSinceClean} 天，建议进行清洗维护`,
      type: 'info'
    });
  }

  /**
   * 设置定时提醒检查
   */
  scheduleReminders() {
    // 每天检查一次
    setInterval(() => {
      this.checkReminders();
    }, 24 * 60 * 60 * 1000);
    
    // 立即检查一次
    setTimeout(() => {
      this.checkReminders();
    }, 5000);
  }

  /**
   * 保存滤芯数据
   */
  saveFilterData() {
    const key = this.deviceId ? `filter_data_${this.deviceId}` : 'filter_data_default';
    storageService.set(key, this.filterData);
  }

  /**
   * 加载滤芯数据
   */
  loadFilterData() {
    const key = this.deviceId ? `filter_data_${this.deviceId}` : 'filter_data_default';
    this.filterData = storageService.get(key, this.filterData);
    
    if (this.filterData.installTime) {
      console.log(`🔧 加载滤芯数据，安装时间: ${new Date(this.filterData.installTime).toLocaleDateString()}`);
    }
  }

  /**
   * 重置滤芯数据（更换新滤芯时使用）
   */
  resetFilter(deviceId) {
    return this.initFilter(deviceId);
  }

  /**
   * 获取滤芯使用报告
   */
  getFilterReport() {
    const status = this.getFilterStatus();
    
    return {
      basic: {
        usageDays: status.usageDays,
        remainingDays: status.remainingDays,
        usagePercent: status.usagePercent,
        cleanCount: status.cleanCount
      },
      status: {
        code: status.status,
        text: status.statusText,
        color: status.statusColor
      },
      timeline: {
        installDate: status.installTime ? new Date(status.installTime).toLocaleDateString() : null,
        lastCleanDate: status.lastCleanTime ? new Date(status.lastCleanTime).toLocaleDateString() : null,
        nextCleanDays: status.nextCleanDays
      },
      recommendations: this.getRecommendations(status)
    };
  }

  /**
   * 获取维护建议
   */
  getRecommendations(status) {
    const recommendations = [];
    
    if (status.status === 2) {
      recommendations.push('立即更换滤芯，确保净化效果');
    } else if (status.status === 1) {
      recommendations.push('建议清洗滤芯，延长使用寿命');
    } else {
      recommendations.push('滤芯状态良好，继续使用');
    }
    
    if (status.nextCleanDays <= 7 && status.nextCleanDays > 0) {
      recommendations.push(`${status.nextCleanDays}天后建议清洗滤芯`);
    }
    
    return recommendations;
  }
}

// 创建单例实例
const filterReminder = new FilterReminder();

// uni-app兼容的导出方式
module.exports = {
  filterReminder,
  default: filterReminder
};
