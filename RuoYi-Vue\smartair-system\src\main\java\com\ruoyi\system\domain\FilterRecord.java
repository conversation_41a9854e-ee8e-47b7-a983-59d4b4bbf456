package com.ruoyi.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 滤芯记录对象 filter_record
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
public class FilterRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 设备ID */
    @Excel(name = "设备ID")
    private String deviceId;

    /** 安装时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "安装时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date installTime;

    /** 最后清洗时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后清洗时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastCleanTime;

    /** 使用小时数 */
    @Excel(name = "使用小时数")
    private Integer usageHours;

    /** 清洗次数 */
    @Excel(name = "清洗次数")
    private Integer cleanCount;

    /** 状态 0正常 1需清洗 2需更换 */
    @Excel(name = "状态", readConverterExp = "0=正常,1=需清洗,2=需更换")
    private Integer status;

    /** 是否已发送提醒 0否 1是 */
    @Excel(name = "是否已发送提醒", readConverterExp = "0=否,1=是")
    private Integer reminderSent;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setDeviceId(String deviceId) 
    {
        this.deviceId = deviceId;
    }

    public String getDeviceId() 
    {
        return deviceId;
    }
    public void setInstallTime(Date installTime) 
    {
        this.installTime = installTime;
    }

    public Date getInstallTime() 
    {
        return installTime;
    }
    public void setLastCleanTime(Date lastCleanTime) 
    {
        this.lastCleanTime = lastCleanTime;
    }

    public Date getLastCleanTime() 
    {
        return lastCleanTime;
    }
    public void setUsageHours(Integer usageHours) 
    {
        this.usageHours = usageHours;
    }

    public Integer getUsageHours() 
    {
        return usageHours;
    }
    public void setCleanCount(Integer cleanCount) 
    {
        this.cleanCount = cleanCount;
    }

    public Integer getCleanCount() 
    {
        return cleanCount;
    }
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }
    public void setReminderSent(Integer reminderSent) 
    {
        this.reminderSent = reminderSent;
    }

    public Integer getReminderSent() 
    {
        return reminderSent;
    }

    /**
     * 计算使用天数
     */
    public Integer getUsageDays() {
        if (usageHours == null) return 0;
        return usageHours / 24;
    }

    /**
     * 检查是否需要清洗（180天）
     */
    public boolean needsCleaning() {
        return getUsageDays() >= 180;
    }

    /**
     * 获取剩余天数
     */
    public Integer getRemainingDays() {
        int usageDays = getUsageDays();
        return Math.max(0, 180 - usageDays);
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userId", getUserId())
            .append("deviceId", getDeviceId())
            .append("installTime", getInstallTime())
            .append("lastCleanTime", getLastCleanTime())
            .append("usageHours", getUsageHours())
            .append("cleanCount", getCleanCount())
            .append("status", getStatus())
            .append("reminderSent", getReminderSent())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
