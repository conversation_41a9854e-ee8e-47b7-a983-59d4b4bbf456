/**
 * 天气和环境数据服务
 * 获取室外PM2.5、空气质量、湿度等数据
 */

// uni-app小程序兼容的导入方式
const apiConfig = require('@/config/api-config.js');
const { QWEATHER_CONFIG, DEV_CONFIG } = apiConfig;
const jwtGenerator = require('./jwt-generator.js');

class WeatherService {
  constructor() {
    // 和风天气API配置
    this.config = QWEATHER_CONFIG;
    this.useJWT = QWEATHER_CONFIG.jwt.enabled;

    // JWT认证配置
    if (this.useJWT) {
      this.apiHost = QWEATHER_CONFIG.jwt.apiHost;
      this.baseUrl = QWEATHER_CONFIG.endpoints.jwtWeather;
      this.geoUrl = QWEATHER_CONFIG.endpoints.jwtGeo;
      this.projectId = QWEATHER_CONFIG.jwt.projectId;
      this.credentialId = QWEATHER_CONFIG.jwt.credentialId;
      this.privateKey = QWEATHER_CONFIG.jwt.privateKey;
      this.tokenExpireTime = QWEATHER_CONFIG.jwt.tokenExpireTime;
    } else {
      // API Key配置
      this.apiKey = QWEATHER_CONFIG.apiKey;
      this.baseUrl = QWEATHER_CONFIG.endpoints.weather;
      this.geoUrl = QWEATHER_CONFIG.endpoints.geo;
    }

    // 数据缓存
    this.cache = new Map();
    this.cacheTimeout = 10 * 60 * 1000; // 10分钟缓存

    // JWT Token缓存
    this.jwtToken = null;
    this.tokenExpireTime = 0;

    // API配置说明
    this.apiInfo = QWEATHER_CONFIG.setup;

    // 检查配置
    this.isConfigured = this.useJWT || this.apiKey !== 'your_qweather_api_key';

    if (!this.isConfigured && DEV_CONFIG.showConfigTips) {
      console.warn('和风天气API未配置，将使用模拟数据');
      console.log('配置步骤:', QWEATHER_CONFIG.setup.steps);
    }

    if (this.useJWT && DEV_CONFIG.showApiLogs) {
      console.log('🔐 使用JWT认证方式访问和风天气API');
    }
  }

  /**
   * 获取JWT Token
   * @returns {Promise<string>} JWT Token
   */
  async getJWTToken() {
    // 检查现有Token是否有效
    if (this.jwtToken && this.tokenExpireTime > Date.now()) {
      return this.jwtToken;
    }

    try {
      console.log('🔐 生成新的JWT Token...');

      // 生成新的JWT Token
      this.jwtToken = await jwtGenerator.generateJWTToken(
        this.privateKey,
        this.credentialId,
        this.projectId,
        this.tokenExpireTime
      );

      // 设置过期时间（提前5分钟过期，确保安全）
      this.tokenExpireTime = Date.now() + (this.tokenExpireTime - 300) * 1000;

      if (DEV_CONFIG.showApiLogs) {
        console.log('✅ JWT Token生成成功');
      }

      return this.jwtToken;

    } catch (error) {
      console.error('🔴 JWT Token生成失败:', error);
      throw new Error('JWT认证失败');
    }
  }

  /**
   * 获取API请求头
   * @returns {Promise<Object>} 请求头对象
   */
  async getRequestHeaders() {
    const headers = {
      'Content-Type': 'application/json'
    };

    if (this.useJWT) {
      // 使用JWT认证
      const token = await this.getJWTToken();
      headers['Authorization'] = `Bearer ${token}`;
    } else {
      // 使用API Key认证
      headers['key'] = this.apiKey;
    }

    return headers;
  }

  /**
   * 获取当前位置的天气数据
   * @param {number} lat 纬度
   * @param {number} lon 经度
   * @returns {Promise<Object>} 天气数据
   */
  async getCurrentWeather(lat, lon) {
    const cacheKey = `weather_${lat.toFixed(4)}_${lon.toFixed(4)}`;
    const cached = this.getCachedData(cacheKey);

    if (cached) {
      console.log('使用缓存的天气数据');
      return cached;
    }

    try {
      // 如果API Key未配置，直接返回模拟数据
      if (!this.isConfigured) {
        if (DEV_CONFIG.showApiLogs) {
          console.log('使用模拟天气数据（API Key未配置）');
        }
        return this.getMockData();
      }

      // 先获取城市ID（和风天气推荐方式）
      const locationId = await this.getLocationId(lat, lon);

      const [weatherData, airData] = await Promise.all([
        this.fetchWeatherData(locationId),
        this.fetchAirQualityData(locationId)
      ]);

      const result = {
        ...weatherData,
        ...airData,
        locationId,
        coordinates: { lat, lon },
        timestamp: Date.now(),
        source: '和风天气'
      };

      this.setCachedData(cacheKey, result);

      if (DEV_CONFIG.showApiLogs) {
        console.log('获取到新的天气数据:', result);
      }

      return result;
    } catch (error) {
      console.error('获取天气数据失败:', error);

      // API调用失败时，返回模拟数据
      if (DEV_CONFIG.useMockData) {
        console.log('API调用失败，使用模拟数据');
        return this.getMockData();
      }

      return this.getDefaultWeatherData();
    }
  }

  /**
   * 获取城市ID（和风天气推荐使用城市ID而不是经纬度）
   */
  async getLocationId(lat, lon) {
    try {
      const headers = await this.getRequestHeaders();
      const requestData = {
        location: `${lon},${lat}`
      };

      // API Key方式需要添加key参数
      if (!this.useJWT) {
        requestData.key = this.apiKey;
      }

      const response = await uni.request({
        url: `${this.geoUrl}/city/lookup`,
        method: 'GET',
        header: headers,
        data: requestData,
        timeout: 10000
      });

      if (response.statusCode === 200 && response.data.code === '200') {
        const locations = response.data.location;
        if (locations && locations.length > 0) {
          if (DEV_CONFIG.showApiLogs) {
            console.log('📍 获取到城市信息:', locations[0]);
          }
          return locations[0].id;
        }
      }

      // 如果获取城市ID失败，使用经纬度
      return `${lon},${lat}`;
    } catch (error) {
      console.warn('获取城市ID失败，使用经纬度:', error);
      return `${lon},${lat}`;
    }
  }

  /**
   * 获取天气基础数据
   */
  async fetchWeatherData(locationId) {
    try {
      const headers = await this.getRequestHeaders();
      const requestData = {
        location: locationId
      };

      // API Key方式需要添加key参数
      if (!this.useJWT) {
        requestData.key = this.apiKey;
      }

      const response = await uni.request({
        url: `${this.baseUrl}/weather/now`,
        method: 'GET',
        header: headers,
        data: requestData,
        timeout: 10000
      });

      if (response.statusCode === 200 && response.data.code === '200') {
        const weather = response.data.now;
        return {
          temperature: parseInt(weather.temp),
          humidity: parseInt(weather.humidity),
          windSpeed: parseInt(weather.windSpeed),
          pressure: parseInt(weather.pressure),
          visibility: parseInt(weather.vis),
          weather: weather.text,
          windDir: weather.windDir,
          feelsLike: parseInt(weather.feelsLike),
          updateTime: weather.obsTime
        };
      } else {
        throw new Error(`天气API错误: ${response.data.code} - ${this.getErrorMessage(response.data.code)}`);
      }
    } catch (error) {
      console.error('获取天气数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取空气质量数据
   */
  async fetchAirQualityData(locationId) {
    try {
      const headers = await this.getRequestHeaders();
      const requestData = {
        location: locationId
      };

      // API Key方式需要添加key参数
      if (!this.useJWT) {
        requestData.key = this.apiKey;
      }

      const response = await uni.request({
        url: `${this.baseUrl}/air/now`,
        method: 'GET',
        header: headers,
        data: requestData,
        timeout: 10000
      });

      if (response.statusCode === 200 && response.data.code === '200') {
        const air = response.data.now;
        const aqi = parseInt(air.aqi) || 0;

        return {
          aqi: aqi,
          pm25: parseInt(air.pm2p5) || 0,
          pm10: parseInt(air.pm10) || 0,
          no2: parseInt(air.no2) || 0,
          so2: parseInt(air.so2) || 0,
          co: parseFloat(air.co) || 0,
          o3: parseInt(air.o3) || 0,
          aqiLevel: this.getAQILevel(aqi),
          primary: air.primary || 'PM2.5',
          category: air.category || '未知',
          pubTime: air.pubTime
        };
      } else {
        console.warn(`空气质量API错误: ${response.data.code}`);
        // 返回默认空气质量数据
        return {
          aqi: 85,
          pm25: 35,
          pm10: 55,
          no2: 25,
          so2: 8,
          co: 0.8,
          o3: 120,
          aqiLevel: this.getAQILevel(85),
          primary: 'PM2.5',
          category: '良',
          pubTime: new Date().toISOString()
        };
      }
    } catch (error) {
      console.error('获取空气质量数据失败:', error);
      // 返回默认空气质量数据
      return {
        aqi: 85,
        pm25: 35,
        pm10: 55,
        no2: 25,
        so2: 8,
        co: 0.8,
        o3: 120,
        aqiLevel: this.getAQILevel(85),
        primary: 'PM2.5',
        category: '良',
        pubTime: new Date().toISOString()
      };
    }
  }

  /**
   * 城市搜索功能
   * @param {string} query 搜索关键词
   * @param {string} adm 行政区划（可选）
   * @param {string} range 搜索范围（可选，如'cn'）
   * @param {number} number 返回结果数量（默认10）
   * @returns {Promise<Array>} 城市列表
   */
  async searchCities(query, adm = '', range = '', number = 10) {
    try {
      const headers = await this.getRequestHeaders();
      const requestData = {
        location: query,
        number: Math.min(number, 20) // 最多20个结果
      };

      if (adm) requestData.adm = adm;
      if (range) requestData.range = range;

      // API Key方式需要添加key参数
      if (!this.useJWT) {
        requestData.key = this.apiKey;
      }

      const response = await uni.request({
        url: `${this.geoUrl}/city/lookup`,
        method: 'GET',
        header: headers,
        data: requestData,
        timeout: 10000
      });

      if (response.statusCode === 200 && response.data.code === '200') {
        const cities = response.data.location || [];

        if (DEV_CONFIG.showApiLogs) {
          console.log(`🔍 搜索"${query}"找到${cities.length}个城市`);
        }

        return cities.map(city => ({
          id: city.id,
          name: city.name,
          adm1: city.adm1, // 省份
          adm2: city.adm2, // 城市
          country: city.country,
          lat: parseFloat(city.lat),
          lon: parseFloat(city.lon),
          rank: parseInt(city.rank),
          type: city.type,
          timezone: city.tz,
          utcOffset: city.utcOffset
        }));
      }

      return [];
    } catch (error) {
      console.error('城市搜索失败:', error);
      return [];
    }
  }

  /**
   * 根据经纬度获取城市信息
   * @param {number} lat 纬度
   * @param {number} lon 经度
   * @returns {Promise<Object>} 城市信息
   */
  async getCityByCoordinates(lat, lon) {
    try {
      const cities = await this.searchCities(`${lon},${lat}`);
      return cities.length > 0 ? cities[0] : null;
    } catch (error) {
      console.error('根据坐标获取城市信息失败:', error);
      return null;
    }
  }

  /**
   * 获取AQI等级
   */
  getAQILevel(aqi) {
    if (aqi <= 50) return { level: 1, text: '优', color: '#00e400' };
    if (aqi <= 100) return { level: 2, text: '良', color: '#ffff00' };
    if (aqi <= 150) return { level: 3, text: '轻度污染', color: '#ff7e00' };
    if (aqi <= 200) return { level: 4, text: '中度污染', color: '#ff0000' };
    if (aqi <= 300) return { level: 5, text: '重度污染', color: '#8f3f97' };
    return { level: 6, text: '严重污染', color: '#7e0023' };
  }

  /**
   * 缓存数据管理
   */
  getCachedData(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    return null;
  }

  setCachedData(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * 默认数据（API失败时使用）
   */
  getDefaultWeatherData() {
    return {
      temperature: 22,
      humidity: 50,
      aqi: 85,
      pm25: 35,
      pm10: 55,
      aqiLevel: { level: 2, text: '良', color: '#ffff00' },
      weather: '多云',
      timestamp: Date.now(),
      isDefault: true
    };
  }

  /**
   * 获取和风天气错误信息
   */
  getErrorMessage(code) {
    const errorMap = {
      '400': '请求错误，请检查请求参数',
      '401': 'API Key错误或已过期',
      '402': '超过访问次数限制',
      '403': '无访问权限',
      '404': '查询的数据不存在',
      '429': '请求过于频繁',
      '500': '服务器内部错误'
    };
    return errorMap[code] || `未知错误: ${code}`;
  }

  /**
   * 检查API Key是否有效
   */
  async validateApiKey() {
    try {
      const response = await uni.request({
        url: `${this.baseUrl}/weather/now`,
        data: {
          location: '101010100', // 北京的城市ID
          key: this.apiKey
        }
      });

      return response.data.code === '200';
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取API使用情况
   */
  getApiInfo() {
    return {
      ...this.apiInfo,
      currentKey: this.apiKey ? this.apiKey.substring(0, 8) + '...' : '未设置',
      setupGuide: {
        step1: '访问 https://dev.qweather.com 注册账号',
        step2: '创建应用获取API Key',
        step3: '在代码中替换 your_qweather_api_key',
        step4: '免费版每天1000次调用限制'
      }
    };
  }

  /**
   * 模拟数据（开发测试用）
   */
  getMockData() {
    return {
      temperature: Math.floor(Math.random() * 15) + 15, // 15-30度
      humidity: Math.floor(Math.random() * 40) + 40,    // 40-80%
      aqi: Math.floor(Math.random() * 200) + 20,        // 20-220
      pm25: Math.floor(Math.random() * 100) + 10,       // 10-110
      pm10: Math.floor(Math.random() * 150) + 20,       // 20-170
      no2: Math.floor(Math.random() * 80) + 10,
      so2: Math.floor(Math.random() * 50) + 5,
      co: Math.random() * 2 + 0.5,
      o3: Math.floor(Math.random() * 100) + 50,
      aqiLevel: this.getAQILevel(Math.floor(Math.random() * 200) + 20),
      weather: ['晴', '多云', '阴', '小雨'][Math.floor(Math.random() * 4)],
      windDir: ['北风', '南风', '东风', '西风'][Math.floor(Math.random() * 4)],
      windSpeed: Math.floor(Math.random() * 20) + 5,
      pressure: Math.floor(Math.random() * 100) + 1000,
      visibility: Math.floor(Math.random() * 20) + 10,
      feelsLike: Math.floor(Math.random() * 15) + 15,
      timestamp: Date.now(),
      source: '模拟数据',
      isMock: true
    };
  }
}

/**
 * 地理位置服务
 */
const { TENCENT_MAP_CONFIG } = apiConfig;

class LocationService {
  constructor() {
    this.currentLocation = null;
    this.watchId = null;
    this.locationCache = null;
    this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存

    // 腾讯地图API配置
    this.mapApiKey = TENCENT_MAP_CONFIG.apiKey;
    this.isMapConfigured = this.mapApiKey !== 'your_tencent_map_key';

    if (!this.isMapConfigured && DEV_CONFIG.showConfigTips) {
      console.warn('腾讯地图API Key未配置，地址解析功能受限');
    }
  }

  /**
   * 获取当前位置
   */
  async getCurrentLocation() {
    // 检查缓存
    if (this.locationCache &&
        Date.now() - this.locationCache.timestamp < this.cacheTimeout) {
      return this.locationCache.data;
    }

    try {
      const location = await this.getLocationFromWX();

      // 获取地址信息
      const address = await this.getAddressFromLocation(
        location.latitude,
        location.longitude
      );

      const result = {
        ...location,
        ...address,
        timestamp: Date.now()
      };

      // 缓存位置信息
      this.locationCache = {
        data: result,
        timestamp: Date.now()
      };

      this.currentLocation = result;
      return result;
    } catch (error) {
      console.error('获取位置失败:', error);
      return this.getDefaultLocation();
    }
  }

  /**
   * 从微信获取位置
   */
  async getLocationFromWX() {
    return new Promise((resolve, reject) => {
      uni.getLocation({
        type: 'gcj02',
        altitude: true,
        success: (res) => {
          resolve({
            latitude: res.latitude,
            longitude: res.longitude,
            accuracy: res.accuracy,
            altitude: res.altitude,
            speed: res.speed
          });
        },
        fail: (error) => {
          reject(new Error('无法获取位置信息'));
        }
      });
    });
  }

  /**
   * 根据坐标获取地址信息
   */
  async getAddressFromLocation(lat, lon) {
    try {
      // 如果腾讯地图API Key未配置，返回默认地址
      if (!this.isMapConfigured) {
        return {
          province: '北京市',
          city: '北京市',
          district: '朝阳区',
          address: '北京市朝阳区（模拟地址）',
          street: '建国路',
          isDefault: true
        };
      }

      // 使用腾讯地图逆地理编码API
      const response = await uni.request({
        url: TENCENT_MAP_CONFIG.endpoints.geocoder,
        data: {
          location: `${lat},${lon}`,
          key: this.mapApiKey,
          get_poi: 1
        }
      });

      if (response.data.status === 0) {
        const result = response.data.result;
        return {
          province: result.ad_info.province,
          city: result.ad_info.city,
          district: result.ad_info.district,
          address: result.formatted_addresses.recommend,
          street: result.address_component.street,
          adcode: result.ad_info.adcode
        };
      } else {
        throw new Error(`地址解析失败: ${response.data.message}`);
      }
    } catch (error) {
      console.error('地址解析错误:', error);
      return {
        province: '未知省份',
        city: '未知城市',
        district: '未知区域',
        address: '位置获取中...',
        street: '',
        error: error.message
      };
    }
  }

  /**
   * 默认位置（北京）
   */
  getDefaultLocation() {
    return {
      latitude: 39.9042,
      longitude: 116.4074,
      accuracy: 100,
      province: '北京市',
      city: '北京市',
      district: '朝阳区',
      address: '北京市朝阳区',
      street: '',
      timestamp: Date.now(),
      isDefault: true
    };
  }

  /**
   * 开始监听位置变化
   */
  startLocationWatch() {
    if (this.watchId) {
      this.stopLocationWatch();
    }

    this.watchId = uni.onLocationChange((res) => {
      this.currentLocation = {
        latitude: res.latitude,
        longitude: res.longitude,
        accuracy: res.accuracy,
        timestamp: Date.now()
      };

      // 触发位置更新事件
      uni.$emit('locationChanged', this.currentLocation);
    });
  }

  /**
   * 停止监听位置变化
   */
  stopLocationWatch() {
    if (this.watchId) {
      uni.offLocationChange(this.watchId);
      this.watchId = null;
    }
  }
}

// 创建单例实例
const weatherService = new WeatherService();
const locationService = new LocationService();

// uni-app小程序兼容的导出方式
module.exports = {
  weatherService,
  locationService,
  default: { weatherService, locationService }
};
