/**
 * 使用时间统计服务
 * 负责记录设备使用时间、会话时长等统计功能
 */

const storageService = require('./storage.js');

class UsageTimer {
  constructor() {
    this.isRunning = false;
    this.sessionStartTime = null;
    this.currentSessionTime = 0;
    this.totalUsageTime = 0;
    this.timer = null;
    this.updateInterval = 1000; // 1秒更新一次
    
    // 加载历史数据
    this.loadUsageData();
  }

  /**
   * 开始计时（设备连接时调用）
   */
  startTimer(deviceId) {
    if (this.isRunning) {
      console.warn('计时器已在运行中');
      return;
    }

    this.deviceId = deviceId;
    this.isRunning = true;
    this.sessionStartTime = Date.now();
    this.currentSessionTime = 0;

    // 启动定时器
    this.timer = setInterval(() => {
      this.updateSessionTime();
    }, this.updateInterval);

    console.log('🕐 使用时间计时开始');
    
    // 触发开始事件
    uni.$emit('usageTimerStart', {
      deviceId: this.deviceId,
      startTime: this.sessionStartTime
    });
  }

  /**
   * 停止计时（设备断开时调用）
   */
  stopTimer() {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }

    // 计算本次会话时长
    const sessionDuration = this.currentSessionTime;
    
    // 更新总使用时间
    this.totalUsageTime += sessionDuration;
    
    // 保存数据
    this.saveUsageData();

    console.log(`🕐 使用时间计时结束，本次使用: ${this.formatTime(sessionDuration)}`);
    
    // 触发结束事件
    uni.$emit('usageTimerStop', {
      deviceId: this.deviceId,
      sessionDuration: sessionDuration,
      totalUsageTime: this.totalUsageTime
    });

    // 重置会话数据
    this.sessionStartTime = null;
    this.currentSessionTime = 0;
    this.deviceId = null;
  }

  /**
   * 更新会话时间
   */
  updateSessionTime() {
    if (!this.isRunning || !this.sessionStartTime) {
      return;
    }

    this.currentSessionTime = Date.now() - this.sessionStartTime;
    
    // 触发时间更新事件
    uni.$emit('usageTimeUpdate', {
      sessionTime: this.currentSessionTime,
      totalTime: this.totalUsageTime + this.currentSessionTime
    });
  }

  /**
   * 获取当前使用统计
   */
  getUsageStats() {
    return {
      isRunning: this.isRunning,
      sessionTime: this.currentSessionTime,
      totalUsageTime: this.totalUsageTime,
      sessionStartTime: this.sessionStartTime,
      deviceId: this.deviceId,
      // 格式化时间
      formatted: {
        sessionTime: this.formatTime(this.currentSessionTime),
        totalTime: this.formatTime(this.totalUsageTime),
        totalDays: Math.floor(this.totalUsageTime / (1000 * 60 * 60 * 24)),
        totalHours: Math.floor(this.totalUsageTime / (1000 * 60 * 60))
      }
    };
  }

  /**
   * 获取今日使用时间
   */
  getTodayUsage() {
    const today = new Date().toDateString();
    const todayData = this.getDailyUsage(today);
    
    return {
      duration: todayData.duration,
      sessions: todayData.sessions,
      formatted: this.formatTime(todayData.duration)
    };
  }

  /**
   * 获取指定日期的使用数据
   */
  getDailyUsage(dateString) {
    const dailyData = storageService.get(`daily_usage_${dateString}`, {
      duration: 0,
      sessions: 0,
      date: dateString
    });
    
    return dailyData;
  }

  /**
   * 保存使用数据
   */
  saveUsageData() {
    // 保存总使用时间
    storageService.set('total_usage_time', this.totalUsageTime);
    
    // 保存今日使用数据
    const today = new Date().toDateString();
    const todayData = this.getDailyUsage(today);
    
    todayData.duration += this.currentSessionTime;
    todayData.sessions += 1;
    
    storageService.set(`daily_usage_${today}`, todayData);
    
    // 保存最后使用时间
    storageService.set('last_usage_time', Date.now());
  }

  /**
   * 加载历史使用数据
   */
  loadUsageData() {
    this.totalUsageTime = storageService.get('total_usage_time', 0);
    console.log(`📊 加载历史使用时间: ${this.formatTime(this.totalUsageTime)}`);
  }

  /**
   * 格式化时间显示
   */
  formatTime(milliseconds) {
    if (!milliseconds || milliseconds < 0) {
      return '0秒';
    }

    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}天${hours % 24}小时${minutes % 60}分钟`;
    } else if (hours > 0) {
      return `${hours}小时${minutes % 60}分钟`;
    } else if (minutes > 0) {
      return `${minutes}分钟${seconds % 60}秒`;
    } else {
      return `${seconds}秒`;
    }
  }

  /**
   * 重置所有数据（谨慎使用）
   */
  resetAllData() {
    this.stopTimer();
    this.totalUsageTime = 0;
    this.currentSessionTime = 0;
    
    // 清除存储数据
    storageService.remove('total_usage_time');
    storageService.remove('last_usage_time');
    
    // 清除今日数据
    const today = new Date().toDateString();
    storageService.remove(`daily_usage_${today}`);
    
    console.log('📊 使用时间数据已重置');
  }

  /**
   * 获取使用统计报告
   */
  getUsageReport() {
    const stats = this.getUsageStats();
    const todayUsage = this.getTodayUsage();
    
    return {
      summary: {
        totalDays: stats.formatted.totalDays,
        totalHours: stats.formatted.totalHours,
        totalFormatted: stats.formatted.totalTime
      },
      today: {
        duration: todayUsage.duration,
        sessions: todayUsage.sessions,
        formatted: todayUsage.formatted
      },
      current: {
        isRunning: stats.isRunning,
        sessionTime: stats.formatted.sessionTime,
        deviceId: stats.deviceId
      }
    };
  }

  /**
   * 销毁计时器
   */
  destroy() {
    this.stopTimer();
    console.log('🕐 使用时间计时器已销毁');
  }
}

// 创建单例实例
const usageTimer = new UsageTimer();

// uni-app兼容的导出方式
module.exports = {
  usageTimer,
  default: usageTimer
};
