package com.ruoyi.system.service;

import java.util.List;
import java.util.Map;
import com.ruoyi.system.domain.DeviceData;

/**
 * 设备数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface IDeviceDataService 
{
    /**
     * 查询设备数据
     * 
     * @param id 设备数据主键
     * @return 设备数据
     */
    public DeviceData selectDeviceDataById(Long id);

    /**
     * 根据用户ID和设备ID查询最新数据
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return 设备数据
     */
    public DeviceData selectLatestDeviceData(Long userId, String deviceId);

    /**
     * 根据用户ID查询设备数据列表
     * 
     * @param userId 用户ID
     * @return 设备数据集合
     */
    public List<DeviceData> selectDeviceDataByUserId(Long userId);

    /**
     * 查询设备数据列表
     * 
     * @param deviceData 设备数据
     * @return 设备数据集合
     */
    public List<DeviceData> selectDeviceDataList(DeviceData deviceData);

    /**
     * 新增设备数据
     * 
     * @param deviceData 设备数据
     * @return 结果
     */
    public int insertDeviceData(DeviceData deviceData);

    /**
     * 修改设备数据
     * 
     * @param deviceData 设备数据
     * @return 结果
     */
    public int updateDeviceData(DeviceData deviceData);

    /**
     * 批量删除设备数据
     * 
     * @param ids 需要删除的设备数据主键集合
     * @return 结果
     */
    public int deleteDeviceDataByIds(Long[] ids);

    /**
     * 删除设备数据信息
     * 
     * @param id 设备数据主键
     * @return 结果
     */
    public int deleteDeviceDataById(Long id);

    /**
     * 保存蓝牙设备数据
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @param data 设备数据
     * @return 结果
     */
    public int saveBluetoothData(Long userId, String deviceId, Map<String, Object> data);

    /**
     * 获取设备实时数据
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return 设备数据
     */
    public Map<String, Object> getRealtimeData(Long userId, String deviceId);

    /**
     * 获取设备历史数据统计
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @param days 天数
     * @return 统计数据
     */
    public Map<String, Object> getDeviceDataStats(Long userId, String deviceId, Integer days);

    /**
     * 控制设备
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @param command 控制命令
     * @param value 控制值
     * @return 结果
     */
    public Map<String, Object> controlDevice(Long userId, String deviceId, String command, Object value);
}
