package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.AirUser;

/**
 * 小程序用户Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface AirUserMapper 
{
    /**
     * 查询小程序用户
     * 
     * @param userId 小程序用户主键
     * @return 小程序用户
     */
    public AirUser selectAirUserByUserId(Long userId);

    /**
     * 根据手机号查询用户
     * 
     * @param phone 手机号
     * @return 小程序用户
     */
    public AirUser selectAirUserByPhone(String phone);

    /**
     * 根据openid查询用户
     * 
     * @param openid 微信openid
     * @return 小程序用户
     */
    public AirUser selectAirUserByOpenid(String openid);

    /**
     * 查询小程序用户列表
     * 
     * @param airUser 小程序用户
     * @return 小程序用户集合
     */
    public List<AirUser> selectAirUserList(AirUser airUser);

    /**
     * 新增小程序用户
     * 
     * @param airUser 小程序用户
     * @return 结果
     */
    public int insertAirUser(AirUser airUser);

    /**
     * 修改小程序用户
     * 
     * @param airUser 小程序用户
     * @return 结果
     */
    public int updateAirUser(AirUser airUser);

    /**
     * 删除小程序用户
     * 
     * @param userId 小程序用户主键
     * @return 结果
     */
    public int deleteAirUserByUserId(Long userId);

    /**
     * 批量删除小程序用户
     * 
     * @param userIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAirUserByUserIds(Long[] userIds);
}
