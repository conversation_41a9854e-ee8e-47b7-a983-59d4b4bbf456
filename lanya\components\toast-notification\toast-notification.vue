<template>
  <view class="toast-container" v-if="visible">
    <view 
      class="toast-content" 
      :class="[`toast-${type}`, { 'toast-show': showAnimation }]"
      @tap="handleTap"
    >
      <!-- 图标 -->
      <view class="toast-icon" v-if="showIcon">
        <text class="icon-text">{{ iconText }}</text>
      </view>
      
      <!-- 内容 -->
      <view class="toast-body">
        <text class="toast-title" v-if="title">{{ title }}</text>
        <text class="toast-message">{{ message }}</text>
      </view>
      
      <!-- 关闭按钮 -->
      <view class="toast-close" v-if="closable" @tap.stop="close">
        <text class="close-icon">×</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ToastNotification',
  
  props: {
    // 显示状态
    show: {
      type: Boolean,
      default: false
    },
    
    // 消息类型
    type: {
      type: String,
      default: 'info', // success, error, warning, info
      validator: (value) => ['success', 'error', 'warning', 'info'].includes(value)
    },
    
    // 标题
    title: {
      type: String,
      default: ''
    },
    
    // 消息内容
    message: {
      type: String,
      required: true
    },
    
    // 显示时长（毫秒）
    duration: {
      type: Number,
      default: 3000
    },
    
    // 是否显示图标
    showIcon: {
      type: Boolean,
      default: true
    },
    
    // 是否可关闭
    closable: {
      type: Boolean,
      default: false
    },
    
    // 点击是否关闭
    clickToClose: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      visible: false,
      showAnimation: false,
      timer: null
    }
  },
  
  computed: {
    // 图标文本
    iconText() {
      const iconMap = {
        success: '✓',
        error: '✕',
        warning: '⚠',
        info: 'ℹ'
      };
      return iconMap[this.type] || 'ℹ';
    }
  },
  
  watch: {
    show: {
      handler(newVal) {
        if (newVal) {
          this.showToast();
        } else {
          this.hideToast();
        }
      },
      immediate: true
    }
  },
  
  methods: {
    /**
     * 显示提示
     */
    showToast() {
      this.visible = true;
      
      // 延迟显示动画，确保DOM已渲染
      this.$nextTick(() => {
        setTimeout(() => {
          this.showAnimation = true;
        }, 50);
      });
      
      // 设置自动关闭
      if (this.duration > 0) {
        this.timer = setTimeout(() => {
          this.close();
        }, this.duration);
      }
    },
    
    /**
     * 隐藏提示
     */
    hideToast() {
      this.showAnimation = false;
      
      // 等待动画完成后隐藏
      setTimeout(() => {
        this.visible = false;
      }, 300);
      
      // 清除定时器
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
    },
    
    /**
     * 关闭提示
     */
    close() {
      this.hideToast();
      this.$emit('close');
    },
    
    /**
     * 点击处理
     */
    handleTap() {
      if (this.clickToClose) {
        this.close();
      }
      this.$emit('click');
    }
  },
  
  beforeDestroy() {
    if (this.timer) {
      clearTimeout(this.timer);
    }
  }
}
</script>

<style scoped>
.toast-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  pointer-events: none;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 100rpx;
}

.toast-content {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border-radius: 16rpx;
  padding: 24rpx 32rpx;
  margin: 0 40rpx;
  max-width: 600rpx;
  min-width: 200rpx;
  display: flex;
  align-items: center;
  pointer-events: auto;
  transform: translateY(-100rpx);
  opacity: 0;
  transition: all 0.3s ease-out;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
}

.toast-show {
  transform: translateY(0);
  opacity: 1;
}

/* 类型样式 */
.toast-success {
  background: linear-gradient(135deg, #52c41a, #389e0d);
}

.toast-error {
  background: linear-gradient(135deg, #ff4d4f, #cf1322);
}

.toast-warning {
  background: linear-gradient(135deg, #faad14, #d48806);
}

.toast-info {
  background: linear-gradient(135deg, #1890ff, #096dd9);
}

.toast-icon {
  margin-right: 16rpx;
  flex-shrink: 0;
}

.icon-text {
  font-size: 32rpx;
  font-weight: bold;
}

.toast-body {
  flex: 1;
  min-width: 0;
}

.toast-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.toast-message {
  display: block;
  font-size: 28rpx;
  line-height: 1.5;
  opacity: 0.9;
  word-break: break-all;
}

.toast-close {
  margin-left: 16rpx;
  padding: 8rpx;
  flex-shrink: 0;
}

.close-icon {
  font-size: 36rpx;
  font-weight: bold;
  opacity: 0.7;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
  .toast-content {
    margin: 0 20rpx;
    padding: 20rpx 24rpx;
  }
  
  .toast-title {
    font-size: 30rpx;
  }
  
  .toast-message {
    font-size: 26rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .toast-content {
    background: rgba(255, 255, 255, 0.9);
    color: #333;
  }
  
  .toast-success {
    background: linear-gradient(135deg, #52c41a, #389e0d);
    color: white;
  }
  
  .toast-error {
    background: linear-gradient(135deg, #ff4d4f, #cf1322);
    color: white;
  }
  
  .toast-warning {
    background: linear-gradient(135deg, #faad14, #d48806);
    color: white;
  }
  
  .toast-info {
    background: linear-gradient(135deg, #1890ff, #096dd9);
    color: white;
  }
}
</style>
