<template>
  <view class="container">
    <view class="header">
      <text class="title">和风天气API测试</text>
      <text class="subtitle">JWT认证 + 城市搜索</text>
    </view>

    <!-- JWT状态 -->
    <view class="card">
      <view class="card-title">JWT认证状态</view>
      <view class="status-item">
        <text class="label">认证方式:</text>
        <text class="value" :class="useJWT ? 'success' : 'warning'">
          {{ useJWT ? 'JWT认证' : 'API Key' }}
        </text>
      </view>
      <view class="status-item">
        <text class="label">Token状态:</text>
        <text class="value" :class="tokenStatus.class">{{ tokenStatus.text }}</text>
      </view>
    </view>

    <!-- 城市搜索 -->
    <view class="card">
      <view class="card-title">城市搜索测试</view>
      <view class="search-box">
        <input 
          class="search-input" 
          v-model="searchQuery" 
          placeholder="输入城市名称搜索..."
          @input="onSearchInput"
        />
        <button class="search-btn" @click="searchCities">搜索</button>
      </view>
      
      <view v-if="searchResults.length > 0" class="search-results">
        <view 
          v-for="city in searchResults" 
          :key="city.id"
          class="city-item"
          @click="selectCity(city)"
        >
          <view class="city-name">{{ city.name }}</view>
          <view class="city-info">{{ city.adm1 }} {{ city.adm2 }}</view>
          <view class="city-coords">{{ city.lat }}, {{ city.lon }}</view>
        </view>
      </view>
    </view>

    <!-- 天气数据 -->
    <view v-if="selectedCity" class="card">
      <view class="card-title">{{ selectedCity.name }} 天气数据</view>
      <button class="test-btn" @click="getWeatherData" :disabled="loading">
        {{ loading ? '获取中...' : '获取天气数据' }}
      </button>
      
      <view v-if="weatherData" class="weather-data">
        <view class="data-section">
          <text class="section-title">天气信息</text>
          <view class="data-item">
            <text class="data-label">温度:</text>
            <text class="data-value">{{ weatherData.temperature }}°C</text>
          </view>
          <view class="data-item">
            <text class="data-label">湿度:</text>
            <text class="data-value">{{ weatherData.humidity }}%</text>
          </view>
          <view class="data-item">
            <text class="data-label">天气:</text>
            <text class="data-value">{{ weatherData.weather }}</text>
          </view>
        </view>
        
        <view class="data-section">
          <text class="section-title">空气质量</text>
          <view class="data-item">
            <text class="data-label">AQI:</text>
            <text class="data-value" :class="getAQIClass(weatherData.aqi)">
              {{ weatherData.aqi }}
            </text>
          </view>
          <view class="data-item">
            <text class="data-label">PM2.5:</text>
            <text class="data-value">{{ weatherData.pm25 }} μg/m³</text>
          </view>
          <view class="data-item">
            <text class="data-label">等级:</text>
            <text class="data-value">{{ weatherData.category }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 错误信息 -->
    <view v-if="errorMessage" class="error-card">
      <text class="error-text">{{ errorMessage }}</text>
    </view>
  </view>
</template>

<script>
const WeatherService = require('@/utils/weather-service.js');

export default {
  data() {
    return {
      weatherService: null,
      useJWT: false,
      searchQuery: '',
      searchResults: [],
      selectedCity: null,
      weatherData: null,
      loading: false,
      errorMessage: ''
    };
  },

  computed: {
    tokenStatus() {
      if (!this.useJWT) {
        return { text: 'API Key模式', class: 'info' };
      }
      
      if (this.weatherService && this.weatherService.jwtToken) {
        return { text: 'Token有效', class: 'success' };
      }
      
      return { text: '未生成', class: 'warning' };
    }
  },

  onLoad() {
    this.initWeatherService();
  },

  methods: {
    initWeatherService() {
      try {
        this.weatherService = new WeatherService();
        this.useJWT = this.weatherService.useJWT;
        console.log('天气服务初始化成功');
      } catch (error) {
        console.error('天气服务初始化失败:', error);
        this.errorMessage = '天气服务初始化失败';
      }
    },

    onSearchInput() {
      // 防抖搜索
      clearTimeout(this.searchTimer);
      this.searchTimer = setTimeout(() => {
        if (this.searchQuery.length >= 2) {
          this.searchCities();
        }
      }, 500);
    },

    async searchCities() {
      if (!this.searchQuery.trim()) return;
      
      try {
        this.errorMessage = '';
        console.log('搜索城市:', this.searchQuery);
        
        this.searchResults = await this.weatherService.searchCities(
          this.searchQuery.trim(),
          '', // adm
          'cn', // 只搜索中国城市
          10 // 最多10个结果
        );
        
        console.log('搜索结果:', this.searchResults);
      } catch (error) {
        console.error('城市搜索失败:', error);
        this.errorMessage = '城市搜索失败: ' + error.message;
      }
    },

    selectCity(city) {
      this.selectedCity = city;
      this.weatherData = null;
      console.log('选择城市:', city);
    },

    async getWeatherData() {
      if (!this.selectedCity) return;
      
      this.loading = true;
      this.errorMessage = '';
      
      try {
        console.log('获取天气数据:', this.selectedCity);
        
        this.weatherData = await this.weatherService.getCurrentWeather(
          this.selectedCity.lat,
          this.selectedCity.lon
        );
        
        console.log('天气数据:', this.weatherData);
      } catch (error) {
        console.error('获取天气数据失败:', error);
        this.errorMessage = '获取天气数据失败: ' + error.message;
      } finally {
        this.loading = false;
      }
    },

    getAQIClass(aqi) {
      if (aqi <= 50) return 'aqi-good';
      if (aqi <= 100) return 'aqi-moderate';
      if (aqi <= 150) return 'aqi-unhealthy-sensitive';
      if (aqi <= 200) return 'aqi-unhealthy';
      if (aqi <= 300) return 'aqi-very-unhealthy';
      return 'aqi-hazardous';
    }
  }
};
</script>

<style scoped>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  margin-top: 10rpx;
}

.card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15rpx;
}

.label {
  color: #666;
}

.value {
  font-weight: bold;
}

.success { color: #52c41a; }
.warning { color: #faad14; }
.info { color: #1890ff; }

.search-box {
  display: flex;
  margin-bottom: 20rpx;
}

.search-input {
  flex: 1;
  padding: 20rpx;
  border: 2rpx solid #d9d9d9;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.search-btn {
  padding: 20rpx 30rpx;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 8rpx;
}

.city-item {
  padding: 20rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 8rpx;
  margin-bottom: 10rpx;
}

.city-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.city-info {
  font-size: 26rpx;
  color: #666;
  margin-top: 5rpx;
}

.city-coords {
  font-size: 24rpx;
  color: #999;
  margin-top: 5rpx;
}

.test-btn {
  width: 100%;
  padding: 25rpx;
  background: #52c41a;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 30rpx;
  margin-bottom: 20rpx;
}

.test-btn:disabled {
  background: #d9d9d9;
}

.weather-data {
  margin-top: 20rpx;
}

.data-section {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.data-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.data-label {
  color: #666;
}

.data-value {
  font-weight: bold;
  color: #333;
}

.aqi-good { color: #52c41a; }
.aqi-moderate { color: #faad14; }
.aqi-unhealthy-sensitive { color: #fa8c16; }
.aqi-unhealthy { color: #f5222d; }
.aqi-very-unhealthy { color: #a0216a; }
.aqi-hazardous { color: #722ed1; }

.error-card {
  background: #fff2f0;
  border: 2rpx solid #ffccc7;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-top: 20rpx;
}

.error-text {
  color: #f5222d;
  font-size: 28rpx;
}
</style>
