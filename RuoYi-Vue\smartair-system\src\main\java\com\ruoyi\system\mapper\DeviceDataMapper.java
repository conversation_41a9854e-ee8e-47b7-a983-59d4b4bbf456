package com.ruoyi.system.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.system.domain.DeviceData;

/**
 * 设备数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface DeviceDataMapper 
{
    /**
     * 查询设备数据
     * 
     * @param id 设备数据主键
     * @return 设备数据
     */
    public DeviceData selectDeviceDataById(Long id);

    /**
     * 根据用户ID和设备ID查询最新数据
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return 设备数据
     */
    public DeviceData selectLatestDeviceData(Long userId, String deviceId);

    /**
     * 根据用户ID查询设备数据列表
     * 
     * @param userId 用户ID
     * @return 设备数据集合
     */
    public List<DeviceData> selectDeviceDataByUserId(Long userId);

    /**
     * 查询设备数据列表
     * 
     * @param deviceData 设备数据
     * @return 设备数据集合
     */
    public List<DeviceData> selectDeviceDataList(DeviceData deviceData);

    /**
     * 新增设备数据
     * 
     * @param deviceData 设备数据
     * @return 结果
     */
    public int insertDeviceData(DeviceData deviceData);

    /**
     * 修改设备数据
     * 
     * @param deviceData 设备数据
     * @return 结果
     */
    public int updateDeviceData(DeviceData deviceData);

    /**
     * 删除设备数据
     * 
     * @param id 设备数据主键
     * @return 结果
     */
    public int deleteDeviceDataById(Long id);

    /**
     * 批量删除设备数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDeviceDataByIds(Long[] ids);

    /**
     * 根据用户ID和设备ID查询最新数据
     *
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return 设备数据
     */
    public DeviceData selectLatestByUserAndDevice(@Param("userId") Long userId, @Param("deviceId") String deviceId);

    /**
     * 获取设备历史数据统计
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @param days 天数
     * @return 统计数据
     */
    public List<DeviceData> selectDeviceDataStats(Long userId, String deviceId, Integer days);
}
