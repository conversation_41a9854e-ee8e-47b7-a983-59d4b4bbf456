package com.ruoyi.system.service;

import java.util.List;
import java.util.Map;
import com.ruoyi.system.domain.FilterRecord;

/**
 * 滤芯记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface IFilterRecordService 
{
    /**
     * 查询滤芯记录
     * 
     * @param id 滤芯记录主键
     * @return 滤芯记录
     */
    public FilterRecord selectFilterRecordById(Long id);

    /**
     * 根据用户ID和设备ID查询滤芯记录
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return 滤芯记录
     */
    public FilterRecord selectFilterRecordByUserDevice(Long userId, String deviceId);

    /**
     * 查询滤芯记录列表
     * 
     * @param filterRecord 滤芯记录
     * @return 滤芯记录集合
     */
    public List<FilterRecord> selectFilterRecordList(FilterRecord filterRecord);

    /**
     * 新增滤芯记录
     * 
     * @param filterRecord 滤芯记录
     * @return 结果
     */
    public int insertFilterRecord(FilterRecord filterRecord);

    /**
     * 修改滤芯记录
     * 
     * @param filterRecord 滤芯记录
     * @return 结果
     */
    public int updateFilterRecord(FilterRecord filterRecord);

    /**
     * 批量删除滤芯记录
     * 
     * @param ids 需要删除的滤芯记录主键集合
     * @return 结果
     */
    public int deleteFilterRecordByIds(Long[] ids);

    /**
     * 删除滤芯记录信息
     * 
     * @param id 滤芯记录主键
     * @return 结果
     */
    public int deleteFilterRecordById(Long id);

    /**
     * 获取滤芯状态
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return 滤芯状态信息
     */
    public Map<String, Object> getFilterStatus(Long userId, String deviceId);

    /**
     * 记录清洗操作
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return 结果
     */
    public int recordCleanOperation(Long userId, String deviceId);

    /**
     * 更新使用时间
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @param hours 增加的小时数
     * @return 结果
     */
    public int updateUsageHours(Long userId, String deviceId, Integer hours);

    /**
     * 检查并发送提醒
     * 
     * @return 发送提醒的数量
     */
    public int checkAndSendReminders();

    /**
     * 初始化滤芯记录
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return 结果
     */
    public int initFilterRecord(Long userId, String deviceId);

    /**
     * 获取提醒信息
     * 
     * @param userId 用户ID
     * @return 提醒信息列表
     */
    public List<Map<String, Object>> getReminderInfo(Long userId);
}
