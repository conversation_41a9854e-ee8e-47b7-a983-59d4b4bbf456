/**
 * API配置文件
 * 请在这里配置各种第三方API的密钥
 */

// 和风天气API配置
const QWEATHER_CONFIG = {
  // JWT认证配置 (推荐使用，更安全)
  jwt: {
    enabled: true,
    apiHost: 'mn564tqped.re.qweatherapi.com',
    projectId: '2BKUGKV7UT',
    credentialId: 'KAGXV4P677',
    privateKey: `-----BEGIN PRIVATE KEY-----
MC4CAQAwBQYDK2VwBCIEIK9B0I1JocEMgJEHtVq9yKgNXjvIOePT+QvRlUgsIkr9
-----END PRIVATE KEY-----`,
    tokenExpireTime: 3600 // 1小时
  },

  // API Key方式 (备用方案)
  apiKey: 'your_qweather_api_key',

  // API端点
  endpoints: {
    // JWT认证使用的端点
    jwtWeather: 'https://mn564tqped.re.qweatherapi.com/v7',
    jwtGeo: 'https://mn564tqped.re.qweatherapi.com/geo/v2',

    // API Key使用的端点
    weather: 'https://devapi.qweather.com/v7',  // 免费版
    geo: 'https://geoapi.qweather.com/v2',      // 地理位置API
    // 付费版可使用: 'https://api.qweather.com/v7'
  },

  // 配置说明
  setup: {
    website: 'https://dev.qweather.com',
    steps: [
      '1. 访问 https://dev.qweather.com 注册账号',
      '2. 创建应用，选择"Web API"类型',
      '3. 获取API Key 或 配置JWT认证',
      '4. JWT认证更安全，推荐使用',
      '5. 免费版每天1000次调用，付费版无限制'
    ],
    pricing: {
      free: '1000次/天',
      standard: '100万次/月 ¥100',
      professional: '500万次/月 ¥500'
    }
  }
};

// 腾讯地图API配置（用于地址解析）
const TENCENT_MAP_CONFIG = {
  // API Key - 请在 https://lbs.qq.com 申请
  apiKey: 'your_tencent_map_key',
  
  // API端点
  endpoints: {
    geocoder: 'https://apis.map.qq.com/ws/geocoder/v1/',
    location: 'https://apis.map.qq.com/ws/location/v1/'
  },
  
  // 配置说明
  setup: {
    website: 'https://lbs.qq.com',
    steps: [
      '1. 访问 https://lbs.qq.com 注册开发者账号',
      '2. 创建应用，选择"WebServiceAPI"',
      '3. 获取API Key',
      '4. 将API Key替换上面的 your_tencent_map_key',
      '5. 免费版每天1万次调用'
    ]
  }
};

// 百度地图API配置（备选方案）
const BAIDU_MAP_CONFIG = {
  apiKey: 'your_baidu_map_key',
  endpoints: {
    geocoder: 'https://api.map.baidu.com/geocoding/v3/',
    location: 'https://api.map.baidu.com/location/v1/'
  }
};

// API配置验证
const validateApiConfig = () => {
  const issues = [];
  
  // 检查和风天气API Key
  if (QWEATHER_CONFIG.apiKey === 'your_qweather_api_key') {
    issues.push({
      service: '和风天气',
      issue: 'API Key未配置',
      solution: '请在 config/api-config.js 中配置和风天气API Key'
    });
  }
  
  // 检查腾讯地图API Key
  if (TENCENT_MAP_CONFIG.apiKey === 'your_tencent_map_key') {
    issues.push({
      service: '腾讯地图',
      issue: 'API Key未配置',
      solution: '请在 config/api-config.js 中配置腾讯地图API Key'
    });
  }
  
  return {
    isValid: issues.length === 0,
    issues
  };
};

// 获取API配置信息
const getApiInfo = () => {
  return {
    qweather: {
      name: '和风天气',
      status: QWEATHER_CONFIG.apiKey !== 'your_qweather_api_key' ? '已配置' : '未配置',
      features: ['实时天气', '空气质量', '地理位置'],
      website: QWEATHER_CONFIG.setup.website
    },
    tencentMap: {
      name: '腾讯地图',
      status: TENCENT_MAP_CONFIG.apiKey !== 'your_tencent_map_key' ? '已配置' : '未配置',
      features: ['地址解析', '逆地理编码'],
      website: TENCENT_MAP_CONFIG.setup.website
    }
  };
};

// 开发模式配置
const DEV_CONFIG = {
  // 是否使用模拟数据（当API Key未配置时）
  useMockData: true,
  
  // 是否显示API调用日志
  showApiLogs: true,
  
  // 是否显示配置提示
  showConfigTips: true
};

// uni-app小程序兼容的导出方式
module.exports = {
  QWEATHER_CONFIG,
  TENCENT_MAP_CONFIG,
  BAIDU_MAP_CONFIG,
  DEV_CONFIG,
  validateApiConfig,
  getApiInfo,
  // 默认导出
  default: {
    qweather: QWEATHER_CONFIG,
    tencentMap: TENCENT_MAP_CONFIG,
    baiduMap: BAIDU_MAP_CONFIG,
    dev: DEV_CONFIG,
    validate: validateApiConfig,
    getInfo: getApiInfo
  }
};
