package com.ruoyi.system.service.impl;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Date;
import java.math.BigDecimal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.system.mapper.DeviceDataMapper;
import com.ruoyi.system.domain.DeviceData;
import com.ruoyi.system.service.IDeviceDataService;

/**
 * 设备数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Service
public class DeviceDataServiceImpl implements IDeviceDataService 
{
    @Autowired
    private DeviceDataMapper deviceDataMapper;

    /**
     * 查询设备数据
     * 
     * @param id 设备数据主键
     * @return 设备数据
     */
    @Override
    public DeviceData selectDeviceDataById(Long id)
    {
        return deviceDataMapper.selectDeviceDataById(id);
    }

    /**
     * 查询设备数据列表
     * 
     * @param deviceData 设备数据
     * @return 设备数据
     */
    @Override
    public List<DeviceData> selectDeviceDataList(DeviceData deviceData)
    {
        return deviceDataMapper.selectDeviceDataList(deviceData);
    }

    /**
     * 新增设备数据
     * 
     * @param deviceData 设备数据
     * @return 结果
     */
    @Override
    public int insertDeviceData(DeviceData deviceData)
    {
        deviceData.setCreateTime(new Date());
        return deviceDataMapper.insertDeviceData(deviceData);
    }

    /**
     * 修改设备数据
     * 
     * @param deviceData 设备数据
     * @return 结果
     */
    @Override
    public int updateDeviceData(DeviceData deviceData)
    {
        return deviceDataMapper.updateDeviceData(deviceData);
    }

    /**
     * 批量删除设备数据
     * 
     * @param ids 需要删除的设备数据主键
     * @return 结果
     */
    @Override
    public int deleteDeviceDataByIds(Long[] ids)
    {
        return deviceDataMapper.deleteDeviceDataByIds(ids);
    }

    /**
     * 删除设备数据信息
     * 
     * @param id 设备数据主键
     * @return 结果
     */
    @Override
    public int deleteDeviceDataById(Long id)
    {
        return deviceDataMapper.deleteDeviceDataById(id);
    }

    /**
     * 保存蓝牙数据
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @param data 数据Map
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveBluetoothData(Long userId, String deviceId, Map<String, Object> data)
    {
        try {
            // 参数验证
            if (userId == null || userId <= 0) {
                throw new IllegalArgumentException("用户ID无效");
            }
            if (deviceId == null || deviceId.trim().isEmpty()) {
                throw new IllegalArgumentException("设备ID无效");
            }
            if (data == null || data.isEmpty()) {
                throw new IllegalArgumentException("数据不能为空");
            }

            DeviceData deviceData = new DeviceData();
            deviceData.setUserId(userId);
            deviceData.setDeviceId(deviceId);

            // 安全地转换数据，添加范围验证
            if (data.get("pm25Indoor") != null) {
                int pm25Indoor = parseIntegerSafely(data.get("pm25Indoor"), "pm25Indoor");
                if (pm25Indoor < 0 || pm25Indoor > 1000) {
                    throw new IllegalArgumentException("室内PM2.5值超出有效范围(0-1000)");
                }
                deviceData.setPm25Indoor(pm25Indoor);
            }
            if (data.get("pm25Outdoor") != null) {
                int pm25Outdoor = parseIntegerSafely(data.get("pm25Outdoor"), "pm25Outdoor");
                if (pm25Outdoor < 0 || pm25Outdoor > 1000) {
                    throw new IllegalArgumentException("室外PM2.5值超出有效范围(0-1000)");
                }
                deviceData.setPm25Outdoor(pm25Outdoor);
            }
            if (data.get("temperature") != null) {
                BigDecimal temperature = parseBigDecimalSafely(data.get("temperature"), "temperature");
                if (temperature.compareTo(new BigDecimal("-50")) < 0 || temperature.compareTo(new BigDecimal("100")) > 0) {
                    throw new IllegalArgumentException("温度值超出有效范围(-50°C到100°C)");
                }
                deviceData.setTemperature(temperature);
            }
            if (data.get("humidity") != null) {
                int humidity = parseIntegerSafely(data.get("humidity"), "humidity");
                if (humidity < 0 || humidity > 100) {
                    throw new IllegalArgumentException("湿度值超出有效范围(0-100%)");
                }
                deviceData.setHumidity(humidity);
            }
            if (data.get("voc") != null) {
                int voc = parseIntegerSafely(data.get("voc"), "voc");
                if (voc < 0 || voc > 10000) {
                    throw new IllegalArgumentException("VOC值超出有效范围(0-10000)");
                }
                deviceData.setVoc(voc);
            }
            if (data.get("buzzerStatus") != null) {
                int buzzerStatus = parseIntegerSafely(data.get("buzzerStatus"), "buzzerStatus");
                if (buzzerStatus < 0 || buzzerStatus > 1) {
                    throw new IllegalArgumentException("蜂鸣器状态值无效(0或1)");
                }
                deviceData.setBuzzerStatus(buzzerStatus);
            }
            if (data.get("fanLevel") != null) {
                int fanLevel = parseIntegerSafely(data.get("fanLevel"), "fanLevel");
                if (fanLevel < 0 || fanLevel > 5) {
                    throw new IllegalArgumentException("风扇档位超出有效范围(0-5)");
                }
                deviceData.setFanLevel(fanLevel);
            }
            if (data.get("powerStatus") != null) {
                int powerStatus = parseIntegerSafely(data.get("powerStatus"), "powerStatus");
                if (powerStatus < 0 || powerStatus > 1) {
                    throw new IllegalArgumentException("电源状态值无效(0或1)");
                }
                deviceData.setPowerStatus(powerStatus);
            }
            if (data.get("location") != null) {
                deviceData.setLocation(data.get("location").toString());
            }
            if (data.get("latitude") != null) {
                deviceData.setLatitude(new BigDecimal(data.get("latitude").toString()));
            }
            if (data.get("longitude") != null) {
                deviceData.setLongitude(new BigDecimal(data.get("longitude").toString()));
            }
            
            return insertDeviceData(deviceData);
        } catch (IllegalArgumentException e) {
            throw e; // 重新抛出参数异常
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("保存蓝牙数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 安全地解析整数
     */
    private int parseIntegerSafely(Object value, String fieldName) {
        if (value == null) {
            throw new IllegalArgumentException(fieldName + "不能为空");
        }
        try {
            if (value instanceof Number) {
                return ((Number) value).intValue();
            }
            return Integer.parseInt(value.toString().trim());
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException(fieldName + "格式不正确: " + value);
        }
    }

    /**
     * 安全地解析BigDecimal
     */
    private BigDecimal parseBigDecimalSafely(Object value, String fieldName) {
        if (value == null) {
            throw new IllegalArgumentException(fieldName + "不能为空");
        }
        try {
            if (value instanceof Number) {
                return new BigDecimal(value.toString());
            }
            return new BigDecimal(value.toString().trim());
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException(fieldName + "格式不正确: " + value);
        }
    }

    /**
     * 获取实时数据
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return 数据Map
     */
    @Override
    public Map<String, Object> getRealtimeData(Long userId, String deviceId)
    {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 查询最新的设备数据
            DeviceData query = new DeviceData();
            query.setUserId(userId);
            query.setDeviceId(deviceId);
            
            List<DeviceData> dataList = deviceDataMapper.selectDeviceDataList(query);
            
            if (dataList != null && !dataList.isEmpty()) {
                DeviceData latest = dataList.get(0); // 假设按时间倒序排列
                
                result.put("pm25Indoor", latest.getPm25Indoor());
                result.put("pm25Outdoor", latest.getPm25Outdoor());
                result.put("temperature", latest.getTemperature());
                result.put("humidity", latest.getHumidity());
                result.put("voc", latest.getVoc());
                result.put("buzzerStatus", latest.getBuzzerStatus());
                result.put("fanLevel", latest.getFanLevel());
                result.put("powerStatus", latest.getPowerStatus());
                result.put("location", latest.getLocation());
                result.put("updateTime", latest.getCreateTime());
            } else {
                // 返回默认数据
                result.put("pm25Indoor", 0);
                result.put("pm25Outdoor", 35);
                result.put("temperature", 22.5);
                result.put("humidity", 65);
                result.put("voc", 0);
                result.put("buzzerStatus", 0);
                result.put("fanLevel", 0);
                result.put("powerStatus", 0);
                result.put("location", "未知");
                result.put("updateTime", new Date());
            }
        } catch (Exception e) {
            e.printStackTrace();
            // 返回默认数据
            result.put("pm25Indoor", 0);
            result.put("pm25Outdoor", 35);
            result.put("temperature", 22.5);
            result.put("humidity", 65);
            result.put("voc", 0);
            result.put("buzzerStatus", 0);
            result.put("fanLevel", 0);
            result.put("powerStatus", 0);
            result.put("location", "未知");
            result.put("updateTime", new Date());
        }
        
        return result;
    }

    /**
     * 获取设备数据统计
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @param days 天数
     * @return 统计数据
     */
    @Override
    public Map<String, Object> getDeviceDataStats(Long userId, String deviceId, Integer days)
    {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 这里应该实现复杂的统计查询
            // 暂时返回模拟数据
            result.put("avgPm25Indoor", 25);
            result.put("avgPm25Outdoor", 45);
            result.put("avgTemperature", 22.5);
            result.put("avgHumidity", 65);
            result.put("totalUsageHours", days * 8);
            result.put("cleanAirHours", days * 6);
            result.put("days", days);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("error", "获取统计数据失败");
        }
        
        return result;
    }

    /**
     * 控制设备
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @param command 命令
     * @param value 值
     * @return 结果
     */
    @Override
    public Map<String, Object> controlDevice(Long userId, String deviceId, String command, Object value)
    {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 这里应该实现设备控制逻辑
            // 暂时返回成功结果
            result.put("success", true);
            result.put("command", command);
            result.put("value", value);
            result.put("message", "控制命令发送成功");
            result.put("timestamp", new Date());
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "控制命令发送失败: " + e.getMessage());
        }
        
        return result;
    }
}
