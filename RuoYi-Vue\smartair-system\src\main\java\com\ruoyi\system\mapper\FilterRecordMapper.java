package com.ruoyi.system.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.ruoyi.system.domain.FilterRecord;

/**
 * 滤芯记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface FilterRecordMapper 
{
    /**
     * 查询滤芯记录
     * 
     * @param id 滤芯记录主键
     * @return 滤芯记录
     */
    public FilterRecord selectFilterRecordById(Long id);

    /**
     * 根据用户ID和设备ID查询滤芯记录
     *
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return 滤芯记录
     */
    public FilterRecord selectFilterRecordByUserDevice(Long userId, String deviceId);

    /**
     * 根据用户ID和设备ID查询滤芯记录（新方法名）
     *
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return 滤芯记录
     */
    public FilterRecord selectByUserAndDevice(@Param("userId") Long userId, @Param("deviceId") String deviceId);

    /**
     * 查询需要提醒的滤芯记录
     * 
     * @return 滤芯记录集合
     */
    public List<FilterRecord> selectFilterRecordsNeedReminder();

    /**
     * 查询滤芯记录列表
     * 
     * @param filterRecord 滤芯记录
     * @return 滤芯记录集合
     */
    public List<FilterRecord> selectFilterRecordList(FilterRecord filterRecord);

    /**
     * 新增滤芯记录
     * 
     * @param filterRecord 滤芯记录
     * @return 结果
     */
    public int insertFilterRecord(FilterRecord filterRecord);

    /**
     * 修改滤芯记录
     * 
     * @param filterRecord 滤芯记录
     * @return 结果
     */
    public int updateFilterRecord(FilterRecord filterRecord);

    /**
     * 删除滤芯记录
     * 
     * @param id 滤芯记录主键
     * @return 结果
     */
    public int deleteFilterRecordById(Long id);

    /**
     * 批量删除滤芯记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFilterRecordByIds(Long[] ids);

    /**
     * 更新使用时间
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @param hours 增加的小时数
     * @return 结果
     */
    public int updateUsageHours(Long userId, String deviceId, Integer hours);

    /**
     * 记录清洗操作
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return 结果
     */
    public int recordCleanOperation(Long userId, String deviceId);
}
