package com.ruoyi.system.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 设备数据对象 device_data
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
public class DeviceData extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 设备ID */
    @Excel(name = "设备ID")
    private String deviceId;

    /** 设备名称 */
    @Excel(name = "设备名称")
    private String deviceName;

    /** 车内PM2.5 */
    @Excel(name = "车内PM2.5")
    private Integer pm25Indoor;

    /** 室外PM2.5 */
    @Excel(name = "室外PM2.5")
    private Integer pm25Outdoor;

    /** 车内温度 */
    @Excel(name = "车内温度")
    private BigDecimal temperature;

    /** 湿度 */
    @Excel(name = "湿度")
    private Integer humidity;

    /** VOC值 */
    @Excel(name = "VOC值")
    private Integer voc;

    /** 负氧离子 */
    @Excel(name = "负氧离子")
    private Integer negativeIon;

    /** 甲醛 */
    @Excel(name = "甲醛")
    private Integer formaldehyde;

    /** 蜂鸣器状态 0关闭 1开启 */
    @Excel(name = "蜂鸣器状态", readConverterExp = "0=关闭,1=开启")
    private Integer buzzerStatus;

    /** 档位 1低 2中 3高 */
    @Excel(name = "档位", readConverterExp = "1=低,2=中,3=高")
    private Integer fanLevel;

    /** 电源状态 0关闭 1开启 */
    @Excel(name = "电源状态", readConverterExp = "0=关闭,1=开启")
    private Integer powerStatus;

    /** 地理位置 */
    @Excel(name = "地理位置")
    private String location;

    /** 纬度 */
    @Excel(name = "纬度")
    private BigDecimal latitude;

    /** 经度 */
    @Excel(name = "经度")
    private BigDecimal longitude;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setDeviceId(String deviceId) 
    {
        this.deviceId = deviceId;
    }

    public String getDeviceId() 
    {
        return deviceId;
    }
    public void setDeviceName(String deviceName) 
    {
        this.deviceName = deviceName;
    }

    public String getDeviceName() 
    {
        return deviceName;
    }
    public void setPm25Indoor(Integer pm25Indoor) 
    {
        this.pm25Indoor = pm25Indoor;
    }

    public Integer getPm25Indoor() 
    {
        return pm25Indoor;
    }
    public void setPm25Outdoor(Integer pm25Outdoor) 
    {
        this.pm25Outdoor = pm25Outdoor;
    }

    public Integer getPm25Outdoor() 
    {
        return pm25Outdoor;
    }
    public void setTemperature(BigDecimal temperature) 
    {
        this.temperature = temperature;
    }

    public BigDecimal getTemperature() 
    {
        return temperature;
    }
    public void setHumidity(Integer humidity) 
    {
        this.humidity = humidity;
    }

    public Integer getHumidity() 
    {
        return humidity;
    }
    public void setVoc(Integer voc) 
    {
        this.voc = voc;
    }

    public Integer getVoc() 
    {
        return voc;
    }
    public void setNegativeIon(Integer negativeIon) 
    {
        this.negativeIon = negativeIon;
    }

    public Integer getNegativeIon() 
    {
        return negativeIon;
    }
    public void setFormaldehyde(Integer formaldehyde) 
    {
        this.formaldehyde = formaldehyde;
    }

    public Integer getFormaldehyde() 
    {
        return formaldehyde;
    }
    public void setBuzzerStatus(Integer buzzerStatus) 
    {
        this.buzzerStatus = buzzerStatus;
    }

    public Integer getBuzzerStatus() 
    {
        return buzzerStatus;
    }
    public void setFanLevel(Integer fanLevel) 
    {
        this.fanLevel = fanLevel;
    }

    public Integer getFanLevel() 
    {
        return fanLevel;
    }
    public void setPowerStatus(Integer powerStatus) 
    {
        this.powerStatus = powerStatus;
    }

    public Integer getPowerStatus() 
    {
        return powerStatus;
    }
    public void setLocation(String location) 
    {
        this.location = location;
    }

    public String getLocation() 
    {
        return location;
    }
    public void setLatitude(BigDecimal latitude) 
    {
        this.latitude = latitude;
    }

    public BigDecimal getLatitude() 
    {
        return latitude;
    }
    public void setLongitude(BigDecimal longitude) 
    {
        this.longitude = longitude;
    }

    public BigDecimal getLongitude() 
    {
        return longitude;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("userId", getUserId())
            .append("deviceId", getDeviceId())
            .append("deviceName", getDeviceName())
            .append("pm25Indoor", getPm25Indoor())
            .append("pm25Outdoor", getPm25Outdoor())
            .append("temperature", getTemperature())
            .append("humidity", getHumidity())
            .append("voc", getVoc())
            .append("negativeIon", getNegativeIon())
            .append("formaldehyde", getFormaldehyde())
            .append("buzzerStatus", getBuzzerStatus())
            .append("fanLevel", getFanLevel())
            .append("powerStatus", getPowerStatus())
            .append("location", getLocation())
            .append("latitude", getLatitude())
            .append("longitude", getLongitude())
            .append("createTime", getCreateTime())
            .toString();
    }
}
