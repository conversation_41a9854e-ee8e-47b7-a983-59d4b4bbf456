package com.ruoyi.system.service.impl;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Date;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import com.ruoyi.system.mapper.AirUserMapper;
import com.ruoyi.system.domain.AirUser;
import com.ruoyi.system.service.IAirUserService;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

/**
 * 小程序用户Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Service
public class AirUserServiceImpl implements IAirUserService 
{
    @Autowired
    private AirUserMapper airUserMapper;

    @Autowired
    private RestTemplate restTemplate;

    @Value("${miniprogram.wechat.appid:demo_appid}")
    private String wechatAppId;

    @Value("${miniprogram.wechat.secret:demo_secret}")
    private String wechatSecret;

    /**
     * 查询小程序用户
     * 
     * @param userId 小程序用户主键
     * @return 小程序用户
     */
    @Override
    public AirUser selectAirUserByUserId(Long userId)
    {
        return airUserMapper.selectAirUserByUserId(userId);
    }

    /**
     * 根据手机号查询用户
     * 
     * @param phone 手机号
     * @return 小程序用户
     */
    @Override
    public AirUser selectAirUserByPhone(String phone)
    {
        return airUserMapper.selectAirUserByPhone(phone);
    }

    /**
     * 根据openid查询用户
     * 
     * @param openid 微信openid
     * @return 小程序用户
     */
    @Override
    public AirUser selectAirUserByOpenid(String openid)
    {
        return airUserMapper.selectAirUserByOpenid(openid);
    }

    /**
     * 查询小程序用户列表
     * 
     * @param airUser 小程序用户
     * @return 小程序用户
     */
    @Override
    public List<AirUser> selectAirUserList(AirUser airUser)
    {
        return airUserMapper.selectAirUserList(airUser);
    }

    /**
     * 新增小程序用户
     * 
     * @param airUser 小程序用户
     * @return 结果
     */
    @Override
    public int insertAirUser(AirUser airUser)
    {
        airUser.setCreateTime(new Date());
        return airUserMapper.insertAirUser(airUser);
    }

    /**
     * 修改小程序用户
     * 
     * @param airUser 小程序用户
     * @return 结果
     */
    @Override
    public int updateAirUser(AirUser airUser)
    {
        airUser.setUpdateTime(new Date());
        return airUserMapper.updateAirUser(airUser);
    }

    /**
     * 批量删除小程序用户
     * 
     * @param userIds 需要删除的小程序用户主键
     * @return 结果
     */
    @Override
    public int deleteAirUserByUserIds(Long[] userIds)
    {
        return airUserMapper.deleteAirUserByUserIds(userIds);
    }

    /**
     * 删除小程序用户信息
     * 
     * @param userId 小程序用户主键
     * @return 结果
     */
    @Override
    public int deleteAirUserByUserId(Long userId)
    {
        return airUserMapper.deleteAirUserByUserId(userId);
    }

    /**
     * 微信登录
     * 
     * @param code 微信授权码
     * @return 用户信息
     */
    @Override
    public AirUser wechatLogin(String code)
    {
        try {
            // 调用微信API获取openid
            String openid = getWechatOpenid(code);
            if (openid == null) {
                throw new RuntimeException("微信登录失败：无法获取用户信息");
            }

            AirUser user = selectAirUserByOpenid(openid);
            if (user == null) {
                // 创建新用户
                user = new AirUser();
                user.setOpenid(openid);
                user.setStatus(1);
                user.setCreateTime(new Date());
                int result = insertAirUser(user);
                if (result <= 0) {
                    throw new RuntimeException("用户创建失败");
                }
                // 重新查询获取生成的ID
                user = selectAirUserByOpenid(openid);
                if (user == null) {
                    throw new RuntimeException("用户创建后查询失败");
                }
            }

            return user;
        } catch (Exception e) {
            throw new RuntimeException("微信登录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 调用微信API获取openid
     */
    private String getWechatOpenid(String code) {
        try {
            // 检查配置
            if (wechatAppId == null || wechatAppId.isEmpty() ||
                wechatSecret == null || wechatSecret.isEmpty() ||
                "demo_appid".equals(wechatAppId) || "demo_secret".equals(wechatSecret)) {
                // 开发环境或模拟模式：返回模拟的openid
                System.out.println("使用模拟微信登录模式，code: " + code);
                return "dev_openid_" + Math.abs(code.hashCode());
            }

            // 构建微信API请求URL
            String url = String.format(
                "https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code",
                wechatAppId, wechatSecret, code
            );

            // 调用微信API
            String response = restTemplate.getForObject(url, String.class);
            if (response == null) {
                return null;
            }

            // 解析响应
            JSONObject jsonResponse = JSON.parseObject(response);
            if (jsonResponse.containsKey("errcode")) {
                int errcode = jsonResponse.getIntValue("errcode");
                if (errcode != 0) {
                    String errmsg = jsonResponse.getString("errmsg");
                    throw new RuntimeException("微信API错误: " + errcode + " - " + errmsg);
                }
            }

            return jsonResponse.getString("openid");
        } catch (Exception e) {
            // 开发环境降级：返回模拟的openid
            return "fallback_openid_" + code;
        }
    }

    /**
     * 手机号登录
     * 
     * @param phone 手机号
     * @param code 验证码
     * @return 用户信息
     */
    @Override
    public AirUser phoneLogin(String phone, String code)
    {
        // 这里应该验证验证码
        // 暂时跳过验证
        
        AirUser user = selectAirUserByPhone(phone);
        if (user == null) {
            // 创建新用户
            user = new AirUser();
            user.setPhone(phone);
            user.setStatus(1);
            user.setCreateTime(new Date());
            int result = insertAirUser(user);
            if (result <= 0) {
                throw new RuntimeException("用户创建失败");
            }
            // 重新查询获取生成的ID
            user = selectAirUserByPhone(phone);
            if (user == null) {
                throw new RuntimeException("用户创建后查询失败");
            }
        }
        
        return user;
    }

    /**
     * 注册用户
     * 
     * @param airUser 用户信息
     * @return 结果
     */
    @Override
    public AirUser registerUser(AirUser airUser)
    {
        airUser.setStatus(1);
        airUser.setCreateTime(new Date());
        int result = insertAirUser(airUser);
        
        if (result > 0) {
            return airUser;
        }
        
        return null;
    }

    /**
     * 更新用户信息
     * 
     * @param airUser 用户信息
     * @return 结果
     */
    @Override
    public int updateUserInfo(AirUser airUser)
    {
        airUser.setUpdateTime(new Date());
        return updateAirUser(airUser);
    }
}
