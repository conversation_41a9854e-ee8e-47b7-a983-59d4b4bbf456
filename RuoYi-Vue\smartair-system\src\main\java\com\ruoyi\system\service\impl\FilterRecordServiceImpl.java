package com.ruoyi.system.service.impl;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.Date;
import java.util.Calendar;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.FilterRecordMapper;
import com.ruoyi.system.domain.FilterRecord;
import com.ruoyi.system.service.IFilterRecordService;

/**
 * 滤芯记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
@Service
public class FilterRecordServiceImpl implements IFilterRecordService 
{
    @Autowired
    private FilterRecordMapper filterRecordMapper;

    /**
     * 查询滤芯记录
     * 
     * @param id 滤芯记录主键
     * @return 滤芯记录
     */
    @Override
    public FilterRecord selectFilterRecordById(Long id)
    {
        return filterRecordMapper.selectFilterRecordById(id);
    }

    /**
     * 查询滤芯记录列表
     * 
     * @param filterRecord 滤芯记录
     * @return 滤芯记录
     */
    @Override
    public List<FilterRecord> selectFilterRecordList(FilterRecord filterRecord)
    {
        return filterRecordMapper.selectFilterRecordList(filterRecord);
    }

    /**
     * 新增滤芯记录
     * 
     * @param filterRecord 滤芯记录
     * @return 结果
     */
    @Override
    public int insertFilterRecord(FilterRecord filterRecord)
    {
        filterRecord.setCreateTime(new Date());
        return filterRecordMapper.insertFilterRecord(filterRecord);
    }

    /**
     * 修改滤芯记录
     * 
     * @param filterRecord 滤芯记录
     * @return 结果
     */
    @Override
    public int updateFilterRecord(FilterRecord filterRecord)
    {
        filterRecord.setUpdateTime(new Date());
        return filterRecordMapper.updateFilterRecord(filterRecord);
    }

    /**
     * 批量删除滤芯记录
     * 
     * @param ids 需要删除的滤芯记录主键
     * @return 结果
     */
    @Override
    public int deleteFilterRecordByIds(Long[] ids)
    {
        return filterRecordMapper.deleteFilterRecordByIds(ids);
    }

    /**
     * 删除滤芯记录信息
     * 
     * @param id 滤芯记录主键
     * @return 结果
     */
    @Override
    public int deleteFilterRecordById(Long id)
    {
        return filterRecordMapper.deleteFilterRecordById(id);
    }

    /**
     * 获取滤芯状态
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return 滤芯状态
     */
    @Override
    public Map<String, Object> getFilterStatus(Long userId, String deviceId)
    {
        Map<String, Object> result = new HashMap<>();
        
        try {
            FilterRecord record = filterRecordMapper.selectByUserAndDevice(userId, deviceId);
            
            if (record == null) {
                // 创建新记录
                record = new FilterRecord();
                record.setUserId(userId);
                record.setDeviceId(deviceId);
                record.setInstallTime(new Date());
                record.setCleanCount(0);
                record.setUsageHours(0);
                record.setStatus(0);
                insertFilterRecord(record);
            }
            
            // 计算使用天数
            long installTime = record.getInstallTime().getTime();
            long currentTime = System.currentTimeMillis();
            int usageDays = (int) ((currentTime - installTime) / (1000 * 60 * 60 * 24));
            
            // 计算剩余天数
            int remainingDays = Math.max(0, 180 - usageDays);
            
            // 判断状态
            int status = 0; // 正常
            if (usageDays >= 180) {
                status = 2; // 需要更换
            } else if (usageDays >= 150) {
                status = 1; // 需要清洗
            }
            
            result.put("usageDays", usageDays);
            result.put("remainingDays", remainingDays);
            result.put("cleanCount", record.getCleanCount());
            result.put("usageHours", record.getUsageHours());
            result.put("status", status);
            result.put("installTime", record.getInstallTime());
            result.put("lastCleanTime", record.getLastCleanTime());
            result.put("deviceName", "空气净化器");
            
        } catch (Exception e) {
            e.printStackTrace();
            // 返回默认数据
            result.put("usageDays", 0);
            result.put("remainingDays", 180);
            result.put("cleanCount", 0);
            result.put("usageHours", 0);
            result.put("status", 0);
            result.put("installTime", new Date());
            result.put("lastCleanTime", null);
            result.put("deviceName", "空气净化器");
        }
        
        return result;
    }

    /**
     * 记录清洗操作
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return 结果
     */
    @Override
    public int recordCleanOperation(Long userId, String deviceId)
    {
        try {
            FilterRecord record = filterRecordMapper.selectByUserAndDevice(userId, deviceId);
            
            if (record == null) {
                // 创建新记录
                record = new FilterRecord();
                record.setUserId(userId);
                record.setDeviceId(deviceId);
                record.setInstallTime(new Date());
                record.setCleanCount(1);
                record.setLastCleanTime(new Date());
                record.setUsageHours(0);
                record.setStatus(0);
                return insertFilterRecord(record);
            } else {
                // 更新记录
                record.setCleanCount(record.getCleanCount() + 1);
                record.setLastCleanTime(new Date());
                record.setInstallTime(new Date()); // 重置安装时间
                record.setStatus(0); // 重置状态
                return updateFilterRecord(record);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 更新使用小时数
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @param hours 小时数
     * @return 结果
     */
    @Override
    public int updateUsageHours(Long userId, String deviceId, Integer hours)
    {
        try {
            FilterRecord record = filterRecordMapper.selectByUserAndDevice(userId, deviceId);
            
            if (record == null) {
                // 创建新记录
                record = new FilterRecord();
                record.setUserId(userId);
                record.setDeviceId(deviceId);
                record.setInstallTime(new Date());
                record.setCleanCount(0);
                record.setUsageHours(hours);
                record.setStatus(0);
                return insertFilterRecord(record);
            } else {
                // 更新使用时间
                record.setUsageHours(hours);
                return updateFilterRecord(record);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 获取提醒信息
     * 
     * @param userId 用户ID
     * @return 提醒列表
     */
    @Override
    public List<Map<String, Object>> getReminderInfo(Long userId)
    {
        List<Map<String, Object>> reminders = new ArrayList<>();
        
        try {
            FilterRecord query = new FilterRecord();
            query.setUserId(userId);
            
            List<FilterRecord> records = filterRecordMapper.selectFilterRecordList(query);
            
            for (FilterRecord record : records) {
                // 计算使用天数
                long installTime = record.getInstallTime().getTime();
                long currentTime = System.currentTimeMillis();
                int usageDays = (int) ((currentTime - installTime) / (1000 * 60 * 60 * 24));
                int remainingDays = Math.max(0, 180 - usageDays);
                
                // 判断是否需要提醒
                if (usageDays >= 180 || remainingDays <= 7) {
                    Map<String, Object> reminder = new HashMap<>();
                    reminder.put("deviceId", record.getDeviceId());
                    reminder.put("deviceName", "空气净化器");
                    reminder.put("usageDays", usageDays);
                    reminder.put("remainingDays", remainingDays);
                    
                    if (usageDays >= 180) {
                        reminder.put("status", 2); // 需要更换
                    } else {
                        reminder.put("status", 1); // 需要清洗
                    }
                    
                    reminders.add(reminder);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        return reminders;
    }

    /**
     * 初始化滤芯记录
     * 
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return 结果
     */
    @Override
    public int initFilterRecord(Long userId, String deviceId)
    {
        try {
            FilterRecord record = filterRecordMapper.selectByUserAndDevice(userId, deviceId);
            
            if (record == null) {
                // 创建新记录
                record = new FilterRecord();
                record.setUserId(userId);
                record.setDeviceId(deviceId);
                record.setInstallTime(new Date());
                record.setCleanCount(0);
                record.setUsageHours(0);
                record.setStatus(0);
                return insertFilterRecord(record);
            } else {
                // 重置记录
                record.setInstallTime(new Date());
                record.setCleanCount(0);
                record.setUsageHours(0);
                record.setStatus(0);
                record.setLastCleanTime(null);
                return updateFilterRecord(record);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }
}
