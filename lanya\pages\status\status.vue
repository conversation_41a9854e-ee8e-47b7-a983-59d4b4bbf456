<template>
  <view class="container">
    <!-- 设备连接状态 -->
    <view class="card connection-status-card">
      <view class="card-header">
        <text class="card-title">设备连接状态</text>
        <view class="status-indicator" :class="connectionStatusClass">
          <text class="status-text">{{ connectionStatusText }}</text>
        </view>
      </view>
      
      <view class="connection-details" v-if="isConnected">
        <view class="detail-item">
          <text class="detail-label">设备名称</text>
          <text class="detail-value">{{ deviceName }}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">连接时间</text>
          <text class="detail-value">{{ formatConnectionTime() }}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">信号强度</text>
          <text class="detail-value" :class="signalStrengthClass">{{ signalStrengthText }}</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">固件版本</text>
          <text class="detail-value">{{ firmwareVersion }}</text>
        </view>
      </view>
      
      <view class="no-connection" v-else>
        <view class="no-connection-icon">📡</view>
        <text class="no-connection-text">设备未连接</text>
        <view class="connection-button" @click="goToDevicePage">
          <text>连接设备</text>
        </view>
      </view>
    </view>

    <!-- 空气质量监控 -->
    <view class="card air-quality-card" v-if="isConnected">
      <view class="card-header">
        <text class="card-title">空气质量监控</text>
        <view class="quality-level" :class="airQualityLevel.class">
          <text class="quality-text">{{ airQualityLevel.text }}</text>
        </view>
      </view>
      
      <view class="quality-grid">
        <view class="quality-item" v-for="item in airQualityData" :key="item.key">
          <view class="quality-icon" :style="{ backgroundColor: item.color }">
            <text class="icon-text">{{ item.icon }}</text>
          </view>
          <view class="quality-info">
            <text class="quality-label">{{ item.label }}</text>
            <view class="quality-value">
              <text class="value-number">{{ item.value }}</text>
              <text class="value-unit">{{ item.unit }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 设备运行状态 -->
    <view class="card device-status-card" v-if="isConnected">
      <view class="card-header">
        <text class="card-title">设备运行状态</text>
        <view class="refresh-button" @click="refreshData">
          <text class="refresh-icon">🔄</text>
        </view>
      </view>
      
      <view class="status-grid">
        <view class="status-item">
          <view class="status-icon power" :class="{ active: deviceStatus.power }">
            <text class="icon-text">⚡</text>
          </view>
          <text class="status-label">电源状态</text>
          <text class="status-value">{{ deviceStatus.power ? '开启' : '关闭' }}</text>
        </view>
        
        <view class="status-item">
          <view class="status-icon speed">
            <text class="icon-text">🌪️</text>
          </view>
          <text class="status-label">风速档位</text>
          <text class="status-value">{{ speedLevels[deviceStatus.speed] }}</text>
        </view>
        
        <view class="status-item">
          <view class="status-icon buzzer" :class="{ active: deviceStatus.buzzer }">
            <text class="icon-text">🔔</text>
          </view>
          <text class="status-label">蜂鸣器</text>
          <text class="status-value">{{ deviceStatus.buzzer ? '开启' : '关闭' }}</text>
        </view>
        
        <view class="status-item">
          <view class="status-icon efficiency">
            <text class="icon-text">📊</text>
          </view>
          <text class="status-label">净化效率</text>
          <text class="status-value">{{ purificationEfficiency }}%</text>
        </view>
      </view>
    </view>

    <!-- 电气参数监控 -->
    <view class="card electrical-card" v-if="isConnected">
      <view class="card-header">
        <text class="card-title">电气参数监控</text>
        <view class="health-indicator" :class="electricalHealthClass">
          <text class="health-text">{{ electricalHealthText }}</text>
        </view>
      </view>
      
      <view class="electrical-grid">
        <view class="electrical-item voltage">
          <view class="electrical-icon">
            <text class="icon-text">⚡</text>
          </view>
          <view class="electrical-info">
            <text class="electrical-label">电压</text>
            <text class="electrical-value" :class="getVoltageClass()">
              {{ electricalData.voltage }} V
            </text>
            <text class="range-text">正常范围: 10.8-13.2V</text>
          </view>
        </view>
        
        <view class="electrical-item current">
          <view class="electrical-icon">
            <text class="icon-text">🔌</text>
          </view>
          <view class="electrical-info">
            <text class="electrical-label">电流</text>
            <text class="electrical-value" :class="getCurrentClass()">
              {{ electricalData.current }} A
            </text>
            <text class="range-text">最大电流: 3.0A</text>
          </view>
        </view>
        
        <view class="electrical-item power">
          <view class="electrical-icon">
            <text class="icon-text">⚙️</text>
          </view>
          <view class="electrical-info">
            <text class="electrical-label">功率</text>
            <text class="electrical-value">
              {{ electricalData.power }} W
            </text>
            <text class="range-text">额定功率: 35W</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 保护状态监控 -->
    <view class="card protection-card" v-if="isConnected">
      <view class="card-header">
        <text class="card-title">保护状态监控</text>
        <view class="protection-status" :class="protectionStatusClass">
          <text class="protection-text">{{ protectionStatusText }}</text>
        </view>
      </view>
      
      <view class="protection-list">
        <view class="protection-item" v-for="item in protectionItems" :key="item.key">
          <view class="protection-icon" :class="{ active: item.active, warning: item.warning }">
            <text class="icon-text">{{ item.icon }}</text>
          </view>
          <view class="protection-info">
            <text class="protection-name">{{ item.name }}</text>
            <text class="protection-desc">{{ item.desc }}</text>
          </view>
          <view class="protection-indicator" :class="item.status">
            <text class="indicator-text">{{ item.statusText }}</text>
          </view>
        </view>
      </view>
      
      <view class="protection-warning" v-if="hasProtectionWarning()">
        <view class="warning-icon">⚠️</view>
        <text class="warning-text">检测到潜在风险，请注意设备状态</text>
      </view>
    </view>

    <!-- 数据更新时间 -->
    <view class="update-info" v-if="isConnected">
      <text class="update-text">最后更新: {{ formatUpdateTime() }}</text>
      <view class="auto-refresh" :class="{ active: autoRefresh }">
        <switch :checked="autoRefresh" @change="toggleAutoRefresh" color="#42B883" />
        <text class="refresh-label">自动刷新</text>
      </view>
    </view>
  </view>
</template>

<script>
import bluetoothService from '@/utils/unified-bluetooth-service.js';

export default {
  name: 'DeviceStatus',
  data() {
    return {
      // 连接状态
      isConnected: false,
      deviceName: '空气净化器',
      connectionTime: null,
      firmwareVersion: 'v1.0.0',
      
      // 设备状态
      deviceStatus: {
        power: false,
        speed: 0,
        buzzer: false,
        timestamp: 0
      },
      
      // 传感器数据
      sensorData: {
        pm25: 0,
        temperature: 0,
        humidity: 0,
        voc: 0,
        timestamp: 0
      },
      
      // 电气参数
      electricalData: {
        voltage: 0,
        current: 0,
        power: 0,
        timestamp: 0
      },
      
      // 保护状态
      protectionData: {
        shortCircuit: false,
        overCurrent: false,
        lowVoltage: false,
        overTemperature: false,
        timestamp: 0
      },
      
      // UI状态
      autoRefresh: true,
      updateTimer: null,
      lastUpdateTime: 0,
      
      // 配置
      speedLevels: ['关闭', '低速', '中速', '高速']
    };
  },
  
  computed: {
    connectionStatusClass() {
      return this.isConnected ? 'connected' : 'disconnected';
    },
    
    connectionStatusText() {
      return this.isConnected ? '已连接' : '未连接';
    },
    
    signalStrengthClass() {
      const dataAge = Date.now() - this.lastUpdateTime;
      if (dataAge < 5000) return 'signal-excellent';
      if (dataAge < 10000) return 'signal-good';
      if (dataAge < 20000) return 'signal-fair';
      return 'signal-poor';
    },
    
    signalStrengthText() {
      const dataAge = Date.now() - this.lastUpdateTime;
      if (dataAge < 5000) return '优秀';
      if (dataAge < 10000) return '良好';
      if (dataAge < 20000) return '一般';
      return '较差';
    },
    
    airQualityLevel() {
      const pm25 = this.sensorData.pm25;
      if (pm25 <= 35) return { class: 'excellent', text: '优秀' };
      if (pm25 <= 75) return { class: 'good', text: '良好' };
      if (pm25 <= 115) return { class: 'moderate', text: '轻度污染' };
      if (pm25 <= 150) return { class: 'poor', text: '中度污染' };
      return { class: 'hazardous', text: '重度污染' };
    },
    
    airQualityData() {
      return [
        {
          key: 'pm25',
          label: 'PM2.5',
          value: this.sensorData.pm25 || '--',
          unit: 'μg/m³',
          icon: '🌫️',
          color: this.getAirQualityColor(this.sensorData.pm25)
        },
        {
          key: 'temperature',
          label: '温度',
          value: this.sensorData.temperature || '--',
          unit: '°C',
          icon: '🌡️',
          color: '#3498db'
        },
        {
          key: 'humidity',
          label: '湿度',
          value: this.sensorData.humidity || '--',
          unit: '%',
          icon: '💧',
          color: '#2ecc71'
        },
        {
          key: 'voc',
          label: 'VOC',
          value: this.sensorData.voc || '--',
          unit: 'ppm',
          icon: '🧪',
          color: '#f39c12'
        }
      ];
    }
  }
};
</script>

<style scoped>
.container {
  padding: 20rpx;
  background-color: #f5f9f5;
  min-height: 100vh;
}

.card {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #2c3e50;
}

.status-indicator {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.status-indicator.connected {
  background: #27ae60;
  color: white;
}

.status-indicator.disconnected {
  background: #e74c3c;
  color: white;
}
</style>
