<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.FilterRecordMapper">
    
    <resultMap type="FilterRecord" id="FilterRecordResult">
        <result property="id"             column="id"             />
        <result property="userId"         column="user_id"        />
        <result property="deviceId"       column="device_id"      />
        <result property="installTime"    column="install_time"   />
        <result property="lastCleanTime"  column="last_clean_time"/>
        <result property="cleanCount"     column="clean_count"    />
        <result property="usageHours"     column="usage_hours"    />
        <result property="status"         column="status"         />
        <result property="createTime"     column="create_time"    />
        <result property="updateTime"     column="update_time"    />
    </resultMap>

    <sql id="selectFilterRecordVo">
        select id, user_id, device_id, install_time, last_clean_time, clean_count, usage_hours, status, create_time, update_time from filter_record
    </sql>

    <select id="selectFilterRecordList" parameterType="FilterRecord" resultMap="FilterRecordResult">
        <include refid="selectFilterRecordVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="deviceId != null  and deviceId != ''"> and device_id = #{deviceId}</if>
            <if test="installTime != null "> and install_time = #{installTime}</if>
            <if test="lastCleanTime != null "> and last_clean_time = #{lastCleanTime}</if>
            <if test="cleanCount != null "> and clean_count = #{cleanCount}</if>
            <if test="usageHours != null "> and usage_hours = #{usageHours}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectFilterRecordById" parameterType="Long" resultMap="FilterRecordResult">
        <include refid="selectFilterRecordVo"/>
        where id = #{id}
    </select>

    <select id="selectByUserAndDevice" parameterType="map" resultMap="FilterRecordResult">
        <include refid="selectFilterRecordVo"/>
        where user_id = #{userId} and device_id = #{deviceId}
        limit 1
    </select>
        
    <insert id="insertFilterRecord" parameterType="FilterRecord" useGeneratedKeys="true" keyProperty="id">
        insert into filter_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="deviceId != null and deviceId != ''">device_id,</if>
            <if test="installTime != null">install_time,</if>
            <if test="lastCleanTime != null">last_clean_time,</if>
            <if test="cleanCount != null">clean_count,</if>
            <if test="usageHours != null">usage_hours,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="deviceId != null and deviceId != ''">#{deviceId},</if>
            <if test="installTime != null">#{installTime},</if>
            <if test="lastCleanTime != null">#{lastCleanTime},</if>
            <if test="cleanCount != null">#{cleanCount},</if>
            <if test="usageHours != null">#{usageHours},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateFilterRecord" parameterType="FilterRecord">
        update filter_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deviceId != null and deviceId != ''">device_id = #{deviceId},</if>
            <if test="installTime != null">install_time = #{installTime},</if>
            <if test="lastCleanTime != null">last_clean_time = #{lastCleanTime},</if>
            <if test="cleanCount != null">clean_count = #{cleanCount},</if>
            <if test="usageHours != null">usage_hours = #{usageHours},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFilterRecordById" parameterType="Long">
        delete from filter_record where id = #{id}
    </delete>

    <delete id="deleteFilterRecordByIds" parameterType="String">
        delete from filter_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
