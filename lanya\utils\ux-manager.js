/**
 * 用户体验管理器
 * 统一管理用户体验相关的功能，包括提示、引导、反馈等
 */

class UXManager {
  constructor() {
    this.toastQueue = []; // 提示队列
    this.isShowingToast = false;
    this.loadingCount = 0; // 加载计数器
    this.guidanceShown = new Set(); // 已显示的引导
    
    // 用户行为统计
    this.userActions = {
      clicks: 0,
      connections: 0,
      errors: 0,
      sessions: 0
    };
    
    this.init();
  }

  /**
   * 初始化
   */
  init() {
    // 加载用户偏好设置
    this.loadUserPreferences();
    
    // 监听应用生命周期
    this.setupLifecycleListeners();
    
    console.log('🎨 用户体验管理器已初始化');
  }

  // ==================== 提示管理 ====================

  /**
   * 显示成功提示
   */
  showSuccess(message, options = {}) {
    this.showToast({
      type: 'success',
      message,
      duration: 2000,
      ...options
    });
  }

  /**
   * 显示错误提示
   */
  showError(message, options = {}) {
    this.showToast({
      type: 'error',
      message,
      duration: 4000,
      closable: true,
      ...options
    });
  }

  /**
   * 显示警告提示
   */
  showWarning(message, options = {}) {
    this.showToast({
      type: 'warning',
      message,
      duration: 3000,
      ...options
    });
  }

  /**
   * 显示信息提示
   */
  showInfo(message, options = {}) {
    this.showToast({
      type: 'info',
      message,
      duration: 2500,
      ...options
    });
  }

  /**
   * 显示自定义提示
   */
  showToast(options) {
    const toast = {
      id: Date.now() + Math.random(),
      type: 'info',
      message: '',
      duration: 3000,
      showIcon: true,
      closable: false,
      ...options
    };

    // 添加到队列
    this.toastQueue.push(toast);
    
    // 如果没有正在显示的提示，立即显示
    if (!this.isShowingToast) {
      this.processToastQueue();
    }
  }

  /**
   * 处理提示队列
   */
  processToastQueue() {
    if (this.toastQueue.length === 0) {
      this.isShowingToast = false;
      return;
    }

    this.isShowingToast = true;
    const toast = this.toastQueue.shift();

    // 触发显示事件
    uni.$emit('showToast', toast);

    // 设置自动处理下一个
    setTimeout(() => {
      this.processToastQueue();
    }, toast.duration + 500);
  }

  // ==================== 加载状态管理 ====================

  /**
   * 显示加载状态
   */
  showLoading(title = '加载中...') {
    this.loadingCount++;
    
    if (this.loadingCount === 1) {
      uni.showLoading({
        title,
        mask: true
      });
    }
  }

  /**
   * 隐藏加载状态
   */
  hideLoading() {
    this.loadingCount = Math.max(0, this.loadingCount - 1);
    
    if (this.loadingCount === 0) {
      uni.hideLoading();
    }
  }

  /**
   * 强制隐藏加载状态
   */
  forceHideLoading() {
    this.loadingCount = 0;
    uni.hideLoading();
  }

  // ==================== 用户引导 ====================

  /**
   * 显示功能引导
   */
  async showGuidance(key, options = {}) {
    // 检查是否已显示过
    if (this.guidanceShown.has(key)) {
      return false;
    }

    const defaultOptions = {
      title: '功能提示',
      content: '',
      showCancel: true,
      cancelText: '知道了',
      confirmText: '立即体验',
      success: () => {}
    };

    const modalOptions = { ...defaultOptions, ...options };

    try {
      const result = await uni.showModal(modalOptions);
      
      // 标记为已显示
      this.guidanceShown.add(key);
      this.saveGuidanceState();
      
      if (result.confirm && options.onConfirm) {
        options.onConfirm();
      }
      
      return result.confirm;
    } catch (error) {
      console.error('显示引导失败:', error);
      return false;
    }
  }

  /**
   * 显示首次使用引导
   */
  async showFirstTimeGuidance() {
    const steps = [
      {
        key: 'bluetooth_connection',
        title: '蓝牙连接',
        content: '首次使用需要连接蓝牙设备，请确保设备已开启并在附近'
      },
      {
        key: 'filter_init',
        title: '滤芯初始化',
        content: '连接设备后，系统会自动初始化滤芯计时，开始180天使用周期'
      },
      {
        key: 'usage_tracking',
        title: '使用统计',
        content: '系统会自动记录设备使用时间，帮助您了解使用习惯'
      }
    ];

    for (const step of steps) {
      const shouldContinue = await this.showGuidance(step.key, {
        title: step.title,
        content: step.content
      });
      
      if (!shouldContinue) {
        break;
      }
    }
  }

  // ==================== 错误处理 ====================

  /**
   * 处理蓝牙错误
   */
  handleBluetoothError(error) {
    this.userActions.errors++;
    
    let message = '蓝牙操作失败';
    let suggestions = [];

    // 根据错误类型提供具体建议
    if (error.errCode === 10001) {
      message = '蓝牙未开启';
      suggestions = ['请开启手机蓝牙功能', '确认蓝牙权限已授予'];
    } else if (error.errCode === 10003) {
      message = '连接失败';
      suggestions = ['请确认设备在附近', '尝试重启设备', '检查设备是否被其他应用占用'];
    } else if (error.errCode === 10006) {
      message = '设备服务未找到';
      suggestions = ['请确认设备型号正确', '尝试重新搜索设备'];
    }

    // 显示错误提示
    this.showError(message, {
      title: '连接问题',
      duration: 0, // 不自动消失
      closable: true
    });

    // 显示解决建议
    if (suggestions.length > 0) {
      setTimeout(() => {
        this.showSuggestions(suggestions);
      }, 1000);
    }
  }

  /**
   * 显示解决建议
   */
  async showSuggestions(suggestions) {
    const content = suggestions.map((s, i) => `${i + 1}. ${s}`).join('\n');
    
    await uni.showModal({
      title: '解决建议',
      content,
      showCancel: false,
      confirmText: '我知道了'
    });
  }

  // ==================== 用户反馈 ====================

  /**
   * 收集用户反馈
   */
  async collectFeedback() {
    try {
      const result = await uni.showModal({
        title: '使用反馈',
        content: '您对当前功能是否满意？',
        confirmText: '很满意',
        cancelText: '有建议'
      });

      if (result.confirm) {
        this.showSuccess('感谢您的反馈！');
      } else {
        // 跳转到反馈页面或显示反馈表单
        this.showFeedbackForm();
      }
    } catch (error) {
      console.error('收集反馈失败:', error);
    }
  }

  /**
   * 显示反馈表单
   */
  showFeedbackForm() {
    // 这里可以跳转到反馈页面或显示反馈弹窗
    uni.navigateTo({
      url: '/pages/feedback/feedback'
    });
  }

  // ==================== 数据统计 ====================

  /**
   * 记录用户行为
   */
  trackAction(action, data = {}) {
    if (this.userActions[action] !== undefined) {
      this.userActions[action]++;
    }

    // 记录详细数据
    const actionData = {
      action,
      timestamp: Date.now(),
      ...data
    };

    // 存储到本地（可选）
    const actions = uni.getStorageSync('user_actions') || [];
    actions.push(actionData);
    
    // 只保留最近100条记录
    if (actions.length > 100) {
      actions.splice(0, actions.length - 100);
    }
    
    uni.setStorageSync('user_actions', actions);
  }

  /**
   * 获取使用统计
   */
  getUsageStats() {
    return {
      ...this.userActions,
      totalActions: Object.values(this.userActions).reduce((sum, count) => sum + count, 0),
      averageSessionTime: this.calculateAverageSessionTime()
    };
  }

  // ==================== 私有方法 ====================

  /**
   * 加载用户偏好设置
   */
  loadUserPreferences() {
    const preferences = uni.getStorageSync('user_preferences') || {};
    this.preferences = {
      showGuidance: true,
      enableHapticFeedback: true,
      animationEnabled: true,
      ...preferences
    };
  }

  /**
   * 保存引导状态
   */
  saveGuidanceState() {
    uni.setStorageSync('guidance_shown', Array.from(this.guidanceShown));
  }

  /**
   * 设置生命周期监听
   */
  setupLifecycleListeners() {
    // 监听应用显示
    uni.onAppShow(() => {
      this.userActions.sessions++;
      this.trackAction('app_show');
    });

    // 监听应用隐藏
    uni.onAppHide(() => {
      this.trackAction('app_hide');
    });
  }

  /**
   * 计算平均会话时间
   */
  calculateAverageSessionTime() {
    // 这里可以根据实际需求实现
    return 0;
  }
}

// 创建单例实例
const uxManager = new UXManager();

// uni-app兼容的导出方式
module.exports = {
  uxManager,
  default: uxManager
};
