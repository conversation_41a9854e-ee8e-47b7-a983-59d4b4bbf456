<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.DeviceDataMapper">
    
    <resultMap type="DeviceData" id="DeviceDataResult">
        <result property="id"            column="id"            />
        <result property="userId"        column="user_id"       />
        <result property="deviceId"      column="device_id"     />
        <result property="pm25Indoor"    column="pm25_indoor"   />
        <result property="pm25Outdoor"   column="pm25_outdoor"  />
        <result property="temperature"   column="temperature"   />
        <result property="humidity"      column="humidity"      />
        <result property="voc"           column="voc"           />
        <result property="buzzerStatus"  column="buzzer_status" />
        <result property="fanLevel"      column="fan_level"     />
        <result property="powerStatus"   column="power_status"  />
        <result property="location"      column="location"      />
        <result property="latitude"      column="latitude"      />
        <result property="longitude"     column="longitude"     />
        <result property="createTime"    column="create_time"   />
    </resultMap>

    <sql id="selectDeviceDataVo">
        select id, user_id, device_id, pm25_indoor, pm25_outdoor, temperature, humidity, voc, buzzer_status, fan_level, power_status, location, latitude, longitude, create_time from device_data
    </sql>

    <select id="selectDeviceDataList" parameterType="DeviceData" resultMap="DeviceDataResult">
        <include refid="selectDeviceDataVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="deviceId != null  and deviceId != ''"> and device_id = #{deviceId}</if>
            <if test="pm25Indoor != null "> and pm25_indoor = #{pm25Indoor}</if>
            <if test="pm25Outdoor != null "> and pm25_outdoor = #{pm25Outdoor}</if>
            <if test="temperature != null "> and temperature = #{temperature}</if>
            <if test="humidity != null "> and humidity = #{humidity}</if>
            <if test="voc != null "> and voc = #{voc}</if>
            <if test="buzzerStatus != null "> and buzzer_status = #{buzzerStatus}</if>
            <if test="fanLevel != null "> and fan_level = #{fanLevel}</if>
            <if test="powerStatus != null "> and power_status = #{powerStatus}</if>
            <if test="location != null  and location != ''"> and location = #{location}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectDeviceDataById" parameterType="Long" resultMap="DeviceDataResult">
        <include refid="selectDeviceDataVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertDeviceData" parameterType="DeviceData" useGeneratedKeys="true" keyProperty="id">
        insert into device_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="deviceId != null and deviceId != ''">device_id,</if>
            <if test="pm25Indoor != null">pm25_indoor,</if>
            <if test="pm25Outdoor != null">pm25_outdoor,</if>
            <if test="temperature != null">temperature,</if>
            <if test="humidity != null">humidity,</if>
            <if test="voc != null">voc,</if>
            <if test="buzzerStatus != null">buzzer_status,</if>
            <if test="fanLevel != null">fan_level,</if>
            <if test="powerStatus != null">power_status,</if>
            <if test="location != null and location != ''">location,</if>
            <if test="latitude != null">latitude,</if>
            <if test="longitude != null">longitude,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="deviceId != null and deviceId != ''">#{deviceId},</if>
            <if test="pm25Indoor != null">#{pm25Indoor},</if>
            <if test="pm25Outdoor != null">#{pm25Outdoor},</if>
            <if test="temperature != null">#{temperature},</if>
            <if test="humidity != null">#{humidity},</if>
            <if test="voc != null">#{voc},</if>
            <if test="buzzerStatus != null">#{buzzerStatus},</if>
            <if test="fanLevel != null">#{fanLevel},</if>
            <if test="powerStatus != null">#{powerStatus},</if>
            <if test="location != null and location != ''">#{location},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateDeviceData" parameterType="DeviceData">
        update device_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="deviceId != null and deviceId != ''">device_id = #{deviceId},</if>
            <if test="pm25Indoor != null">pm25_indoor = #{pm25Indoor},</if>
            <if test="pm25Outdoor != null">pm25_outdoor = #{pm25Outdoor},</if>
            <if test="temperature != null">temperature = #{temperature},</if>
            <if test="humidity != null">humidity = #{humidity},</if>
            <if test="voc != null">voc = #{voc},</if>
            <if test="buzzerStatus != null">buzzer_status = #{buzzerStatus},</if>
            <if test="fanLevel != null">fan_level = #{fanLevel},</if>
            <if test="powerStatus != null">power_status = #{powerStatus},</if>
            <if test="location != null and location != ''">location = #{location},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDeviceDataById" parameterType="Long">
        delete from device_data where id = #{id}
    </delete>

    <delete id="deleteDeviceDataByIds" parameterType="String">
        delete from device_data where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据用户和设备查询最新数据 -->
    <select id="selectLatestByUserAndDevice" parameterType="map" resultMap="DeviceDataResult">
        <include refid="selectDeviceDataVo"/>
        where user_id = #{userId} and device_id = #{deviceId}
        order by create_time desc
        limit 1
    </select>
</mapper>
