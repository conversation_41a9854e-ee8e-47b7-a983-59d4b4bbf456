# 和风天气API集成说明

## 📋 概述

本项目已成功集成和风天气API，支持JWT认证方式，提供天气预报、空气质量监测和城市搜索功能。

## 🔧 API配置

### JWT认证信息
- **API Host**: `mn564tqped.re.qweatherapi.com`
- **项目ID**: `2BKUGKV7UT`
- **凭据ID**: `KAGXV4P677`
- **私钥**: 已配置在 `config/api-config.js`

### 支持的API端点

#### 1. 天气预报
```
GET /v7/weather/now?location={locationId}
Authorization: Bearer {jwt_token}
```

#### 2. 空气质量
```
GET /airquality/v1/current/{latitude}/{longitude}
Authorization: Bearer {jwt_token}
```

#### 3. 城市搜索
```
GET /geo/v2/city/lookup?location={query}
Authorization: Bearer {jwt_token}
```

## 🚀 使用方法

### 基本用法

```javascript
// 引入天气服务
const WeatherService = require('@/utils/weather-service.js');

// 创建实例
const weatherService = new WeatherService();

// 获取天气数据
const weatherData = await weatherService.getCurrentWeather(39.9042, 116.4074);
console.log('天气数据:', weatherData);

// 搜索城市
const cities = await weatherService.searchCities('北京');
console.log('城市列表:', cities);
```

### 返回数据格式

#### 天气数据
```javascript
{
  temperature: 25,        // 温度 (°C)
  humidity: 65,          // 湿度 (%)
  weather: "晴",         // 天气状况
  windSpeed: 12,         // 风速 (km/h)
  windDir: "东南风",     // 风向
  pressure: 1013,        // 气压 (hPa)
  visibility: 10,        // 能见度 (km)
  feelsLike: 27,         // 体感温度 (°C)
  updateTime: "2024-01-15T14:30+08:00"
}
```

#### 空气质量数据
```javascript
{
  aqi: 85,              // 空气质量指数
  pm25: 35,             // PM2.5 (μg/m³)
  pm10: 55,             // PM10 (μg/m³)
  no2: 25,              // 二氧化氮 (μg/m³)
  so2: 8,               // 二氧化硫 (μg/m³)
  co: 0.8,              // 一氧化碳 (mg/m³)
  o3: 120,              // 臭氧 (μg/m³)
  aqiLevel: "良",       // 空气质量等级
  primary: "PM2.5",     // 主要污染物
  category: "良",       // 质量类别
  pubTime: "2024-01-15T14:00+08:00"
}
```

#### 城市搜索结果
```javascript
[
  {
    id: "101010100",      // 城市ID
    name: "北京",         // 城市名称
    adm1: "北京市",       // 省份
    adm2: "北京",         // 城市
    country: "中国",      // 国家
    lat: 39.90499,        // 纬度
    lon: 116.40529,       // 经度
    rank: 10,             // 城市等级
    type: "city",         // 类型
    timezone: "Asia/Shanghai",
    utcOffset: "+08:00"
  }
]
```

## 🧪 测试验证

### 1. 使用测试工具
```javascript
const apiTester = require('@/utils/api-test.js');

// 运行所有测试
apiTester.runAllTests().then(results => {
  console.log('测试结果:', results);
});
```

### 2. 使用测试页面
访问测试页面：`pages/test/weather-test`
- 测试JWT认证状态
- 测试城市搜索功能
- 测试天气数据获取

## 🔍 功能特性

### JWT认证
- ✅ 自动生成和管理JWT Token
- ✅ Token过期自动刷新
- ✅ 安全的EdDSA签名算法

### 数据缓存
- ✅ 10分钟数据缓存
- ✅ 减少API调用次数
- ✅ 提升响应速度

### 错误处理
- ✅ 完善的异常处理机制
- ✅ API调用失败时的降级策略
- ✅ 详细的错误日志记录

### 城市搜索
- ✅ 支持模糊搜索
- ✅ 支持行政区划过滤
- ✅ 支持国家范围限制

## 📊 API限制

### 调用频率
- 免费版：1000次/天
- 标准版：100万次/月
- 专业版：500万次/月

### 数据更新频率
- 天气数据：每小时更新
- 空气质量：每小时更新
- 城市数据：实时查询

## 🛠️ 故障排除

### 常见问题

#### 1. JWT Token生成失败
- 检查私钥格式是否正确
- 确认项目ID和凭据ID配置正确
- 查看控制台错误信息

#### 2. API调用失败
- 检查网络连接
- 确认API Host地址正确
- 查看响应状态码和错误信息

#### 3. 数据格式异常
- 检查API响应结构
- 确认数据解析逻辑
- 查看返回的错误码

### 调试技巧

#### 启用详细日志
```javascript
// 在 config/api-config.js 中设置
const DEV_CONFIG = {
  showApiLogs: true,     // 显示API调用日志
  showConfigTips: true,  // 显示配置提示
  useMockData: false     // 禁用模拟数据
};
```

#### 查看网络请求
- 使用浏览器开发者工具
- 检查请求头和响应数据
- 确认JWT Token格式正确

## 📈 性能优化

### 缓存策略
- 天气数据缓存10分钟
- 城市搜索结果缓存1小时
- JWT Token缓存至过期前5分钟

### 请求优化
- 并发请求天气和空气质量数据
- 使用Gzip压缩减少传输量
- 合理设置请求超时时间

## 🔮 扩展功能

### 可添加的功能
- 天气预警信息
- 15天天气预报
- 逐小时天气预报
- 生活指数查询
- 历史天气数据

### 集成建议
- 在首页显示当前位置天气
- 在设备控制页面显示室外空气质量
- 添加城市管理功能
- 支持多城市天气对比

## 📞 技术支持

如遇问题，请检查：
1. 网络连接是否正常
2. API配置是否正确
3. JWT Token是否有效
4. 查看控制台错误信息

更多信息请参考：
- [和风天气开发文档](https://dev.qweather.com/docs/)
- [JWT认证说明](https://dev.qweather.com/docs/configuration/authentication/)
- [API错误码说明](https://dev.qweather.com/docs/resource/status-code/)
