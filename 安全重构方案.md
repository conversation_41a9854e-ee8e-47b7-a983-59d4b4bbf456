# SmartAir 安全重构方案

## 🎯 目标
在确保项目正常使用的前提下，逐步完成 RuoYi → SmartAir 的重构

## 🛡️ 安全原则
1. **每次只改一小部分**
2. **每次改完都测试**
3. **出问题立即回滚**
4. **优先保证功能可用**

## 📋 分阶段重构计划

### 阶段1：验证当前状态 ✅ (已完成)
- [x] 备份原项目 → RuoYi-Vue-backup
- [x] 重命名子模块目录
- [x] 更新 Maven 配置
- [x] 更新基础项目信息

### 阶段2：最小化配置调整 (进行中)
**目标：让项目能正常启动**

#### 2.1 修正关键配置文件
- [ ] 修正 application.yml 中的基础配置
- [ ] 保持数据库连接不变
- [ ] 保持包扫描路径不变（暂时）

#### 2.2 测试编译和启动
- [ ] 测试 Maven 编译
- [ ] 测试后端启动
- [ ] 测试前端启动
- [ ] 测试小程序连接

### 阶段3：渐进式包名替换 (待进行)
**目标：逐步替换包名，确保每步都可用**

#### 3.1 替换非核心包
- [ ] 先替换工具类包名
- [ ] 测试编译和功能
- [ ] 确认无问题后继续

#### 3.2 替换核心包
- [ ] 替换 controller 包名
- [ ] 替换 service 包名
- [ ] 替换 domain 包名

#### 3.3 每次替换后验证
- [ ] 编译测试
- [ ] 功能测试
- [ ] 小程序连接测试

### 阶段4：界面和品牌更新 (最后进行)
**目标：更新用户可见的界面元素**

#### 4.1 后端管理界面
- [ ] 更新页面标题
- [ ] 更新 Logo 和品牌信息
- [ ] 更新版权声明

#### 4.2 小程序界面
- [ ] 更新小程序标题
- [ ] 更新品牌信息

## 🔧 当前状态分析

### ✅ 已完成且稳定的部分：
1. **Maven 项目结构** - 所有 pom.xml 已正确配置
2. **模块依赖关系** - 内部依赖已更新
3. **项目基础信息** - 项目名称、描述已更新
4. **子模块目录** - 所有目录已重命名

### ⚠️ 需要谨慎处理的部分：
1. **Java 包名** - 影响编译，需要分批处理
2. **配置文件** - 影响启动，需要逐个验证
3. **数据库配置** - 影响数据访问，需要保持兼容

### 🎯 当前优先级：
1. **确保项目能编译** (最高优先级)
2. **确保项目能启动** (高优先级)
3. **确保小程序能连接** (高优先级)
4. **完成品牌替换** (中优先级)

## 🚀 下一步行动计划

### 立即执行：
1. **验证当前项目编译状态**
2. **修正阻止编译的问题**
3. **测试基础功能**

### 后续执行：
1. **分批替换包名**
2. **逐步更新配置**
3. **最后更新界面**

## 🔄 回滚策略
如果任何步骤出现问题：
```bash
# 立即停止当前操作
# 从备份恢复
rm -rf RuoYi-Vue
cp -r RuoYi-Vue-backup RuoYi-Vue
```

## 📊 成功标准
每个阶段完成后必须满足：
- ✅ 项目能正常编译
- ✅ 后端能正常启动
- ✅ 前端能正常访问
- ✅ 小程序能正常连接
- ✅ 核心功能正常工作

## 🎉 最终目标
- 完全去除 RuoYi 品牌元素
- 项目变成 SmartAir 专属系统
- 所有功能保持完整可用
- 代码结构清晰专业
