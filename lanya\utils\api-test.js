/**
 * API测试工具
 * 用于验证和风天气API是否正常工作
 */

const jwtGenerator = require('./jwt-generator.js');

class APITester {
  constructor() {
    this.config = {
      apiHost: 'mn564tqped.re.qweatherapi.com',
      projectId: '2BKUGKV7UT',
      credentialId: 'KAGXV4P677',
      privateKey: `-----BEGIN PRIVATE KEY-----
MC4CAQAwBQYDK2VwBCIEIK9B0I1JocEMgJEHtVq9yKgNXjvIOePT+QvRlUgsIkr9
-----END PRIVATE KEY-----`,
      tokenExpireTime: 3600
    };
    
    this.jwtToken = null;
  }

  /**
   * 生成JWT Token
   */
  async generateToken() {
    try {
      console.log('🔐 生成JWT Token...');
      
      this.jwtToken = await jwtGenerator.generateJWTToken(
        this.config.privateKey,
        this.config.credentialId,
        this.config.projectId,
        this.config.tokenExpireTime
      );
      
      console.log('✅ JWT Token生成成功');
      console.log('Token:', this.jwtToken.substring(0, 50) + '...');
      
      return this.jwtToken;
    } catch (error) {
      console.error('❌ JWT Token生成失败:', error);
      throw error;
    }
  }

  /**
   * 测试天气API
   */
  async testWeatherAPI() {
    try {
      console.log('\n🌤️ 测试天气API...');
      
      if (!this.jwtToken) {
        await this.generateToken();
      }

      const response = await uni.request({
        url: `https://${this.config.apiHost}/v7/weather/now`,
        method: 'GET',
        header: {
          'Authorization': `Bearer ${this.jwtToken}`,
          'Content-Type': 'application/json'
        },
        data: {
          location: '101010100' // 北京
        },
        timeout: 10000
      });

      console.log('天气API响应状态:', response.statusCode);
      console.log('天气API响应数据:', response.data);

      if (response.statusCode === 200 && response.data.code === '200') {
        console.log('✅ 天气API测试成功');
        const weather = response.data.now;
        console.log(`📊 北京天气: ${weather.text}, ${weather.temp}°C, 湿度${weather.humidity}%`);
        return true;
      } else {
        console.log('❌ 天气API测试失败');
        return false;
      }
    } catch (error) {
      console.error('❌ 天气API测试异常:', error);
      return false;
    }
  }

  /**
   * 测试空气质量API
   */
  async testAirQualityAPI() {
    try {
      console.log('\n🌫️ 测试空气质量API...');
      
      if (!this.jwtToken) {
        await this.generateToken();
      }

      // 使用坐标格式：北京天安门广场
      const lat = 39.90;
      const lon = 116.40;

      const response = await uni.request({
        url: `https://${this.config.apiHost}/airquality/v1/current/${lat}/${lon}`,
        method: 'GET',
        header: {
          'Authorization': `Bearer ${this.jwtToken}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });

      console.log('空气质量API响应状态:', response.statusCode);
      console.log('空气质量API响应数据:', response.data);

      if (response.statusCode === 200 && response.data.code === '200') {
        console.log('✅ 空气质量API测试成功');
        const air = response.data.now;
        console.log(`🌫️ 北京空气质量: AQI ${air.aqi}, PM2.5 ${air.pm2p5}μg/m³`);
        return true;
      } else {
        console.log('❌ 空气质量API测试失败');
        return false;
      }
    } catch (error) {
      console.error('❌ 空气质量API测试异常:', error);
      return false;
    }
  }

  /**
   * 测试城市搜索API
   */
  async testCitySearchAPI() {
    try {
      console.log('\n🔍 测试城市搜索API...');
      
      if (!this.jwtToken) {
        await this.generateToken();
      }

      const response = await uni.request({
        url: `https://${this.config.apiHost}/geo/v2/city/lookup`,
        method: 'GET',
        header: {
          'Authorization': `Bearer ${this.jwtToken}`,
          'Content-Type': 'application/json'
        },
        data: {
          location: '北京',
          number: 5
        },
        timeout: 10000
      });

      console.log('城市搜索API响应状态:', response.statusCode);
      console.log('城市搜索API响应数据:', response.data);

      if (response.statusCode === 200 && response.data.code === '200') {
        console.log('✅ 城市搜索API测试成功');
        const cities = response.data.location;
        console.log(`🏙️ 找到${cities.length}个城市:`);
        cities.forEach(city => {
          console.log(`  - ${city.name} (${city.adm1} ${city.adm2}) ID: ${city.id}`);
        });
        return true;
      } else {
        console.log('❌ 城市搜索API测试失败');
        return false;
      }
    } catch (error) {
      console.error('❌ 城市搜索API测试异常:', error);
      return false;
    }
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始API测试...\n');
    
    const results = {
      token: false,
      weather: false,
      airQuality: false,
      citySearch: false
    };

    try {
      // 1. 测试JWT Token生成
      results.token = await this.generateToken() !== null;
      
      // 2. 测试天气API
      results.weather = await this.testWeatherAPI();
      
      // 3. 测试空气质量API
      results.airQuality = await this.testAirQualityAPI();
      
      // 4. 测试城市搜索API
      results.citySearch = await this.testCitySearchAPI();
      
    } catch (error) {
      console.error('❌ 测试过程中发生错误:', error);
    }

    // 输出测试结果
    console.log('\n📊 测试结果汇总:');
    console.log('==================');
    console.log(`🔐 JWT Token生成: ${results.token ? '✅ 成功' : '❌ 失败'}`);
    console.log(`🌤️ 天气API: ${results.weather ? '✅ 成功' : '❌ 失败'}`);
    console.log(`🌫️ 空气质量API: ${results.airQuality ? '✅ 成功' : '❌ 失败'}`);
    console.log(`🔍 城市搜索API: ${results.citySearch ? '✅ 成功' : '❌ 失败'}`);
    
    const successCount = Object.values(results).filter(Boolean).length;
    const totalCount = Object.keys(results).length;
    
    console.log(`\n🎯 总体成功率: ${successCount}/${totalCount} (${Math.round(successCount/totalCount*100)}%)`);
    
    if (successCount === totalCount) {
      console.log('🎉 所有API测试通过！可以正常使用。');
    } else {
      console.log('⚠️ 部分API测试失败，请检查配置或网络连接。');
    }

    return results;
  }
}

// 创建单例实例
const apiTester = new APITester();

// uni-app兼容的导出方式
module.exports = apiTester;
