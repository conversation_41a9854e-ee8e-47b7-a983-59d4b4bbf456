/**
 * 性能优化配置
 * 统一管理小程序性能相关的配置参数
 */

// 蓝牙性能配置
const BLUETOOTH_PERFORMANCE = {
  // 连接超时时间
  connectionTimeout: 15000, // 15秒
  
  // 数据查询间隔
  dataQueryInterval: 2000, // 2秒查询一次
  
  // 心跳间隔
  heartbeatInterval: 30000, // 30秒心跳
  
  // 重连配置
  reconnect: {
    enabled: true,
    maxAttempts: 3,
    interval: 5000, // 5秒后重连
    backoffMultiplier: 1.5 // 重连间隔递增倍数
  },
  
  // 数据缓存配置
  cache: {
    maxSize: 100, // 最多缓存100条数据
    ttl: 60000 // 缓存1分钟
  }
};

// 数据更新性能配置
const DATA_UPDATE_PERFORMANCE = {
  // 数值动画配置
  animation: {
    duration: 1000, // 1秒动画时长
    easing: 'ease-out',
    fps: 60 // 目标帧率
  },
  
  // 数据更新频率限制
  updateThrottle: {
    pm25: 1000, // PM2.5数据1秒更新一次
    temperature: 2000, // 温度2秒更新一次
    humidity: 2000, // 湿度2秒更新一次
    general: 500 // 一般数据500ms更新一次
  },
  
  // 批量更新配置
  batchUpdate: {
    enabled: true,
    batchSize: 10, // 批量处理10个更新
    delay: 100 // 100ms内的更新合并处理
  }
};

// 网络请求性能配置
const NETWORK_PERFORMANCE = {
  // 请求超时时间
  timeout: 10000, // 10秒
  
  // 重试配置
  retry: {
    maxAttempts: 3,
    delay: 1000, // 1秒后重试
    backoffMultiplier: 2
  },
  
  // 并发控制
  concurrency: {
    maxConcurrent: 5, // 最多5个并发请求
    queueSize: 20 // 队列最大20个请求
  },
  
  // 缓存配置
  cache: {
    enabled: true,
    defaultTTL: 300000, // 默认缓存5分钟
    maxSize: 50 // 最多缓存50个响应
  }
};

// 内存管理配置
const MEMORY_MANAGEMENT = {
  // 数据清理配置
  cleanup: {
    interval: 300000, // 5分钟清理一次
    maxHistorySize: 1000, // 最多保留1000条历史数据
    maxLogSize: 500 // 最多保留500条日志
  },
  
  // 图片优化
  image: {
    maxWidth: 750, // 最大宽度750px
    maxHeight: 1334, // 最大高度1334px
    quality: 0.8, // 压缩质量80%
    format: 'webp' // 优先使用webp格式
  },
  
  // 存储优化
  storage: {
    maxSize: 10 * 1024 * 1024, // 最大10MB存储
    cleanupThreshold: 0.8, // 80%时开始清理
    compressionEnabled: true // 启用压缩存储
  }
};

// UI性能配置
const UI_PERFORMANCE = {
  // 列表渲染优化
  list: {
    virtualScrollEnabled: true, // 启用虚拟滚动
    itemHeight: 100, // 列表项高度
    bufferSize: 10, // 缓冲区大小
    recycleThreshold: 50 // 超过50项启用回收
  },
  
  // 页面切换优化
  navigation: {
    preloadEnabled: true, // 启用预加载
    transitionDuration: 300, // 切换动画300ms
    cachePages: 3 // 缓存3个页面
  },
  
  // 渲染优化
  rendering: {
    useGPU: true, // 启用GPU加速
    layerize: true, // 启用图层化
    willChange: ['transform', 'opacity'] // 预告变化属性
  }
};

// 调试和监控配置
const DEBUG_PERFORMANCE = {
  // 性能监控
  monitoring: {
    enabled: true, // 启用性能监控
    sampleRate: 0.1, // 10%采样率
    reportInterval: 60000 // 1分钟上报一次
  },
  
  // 日志配置
  logging: {
    level: 'info', // 日志级别
    maxSize: 1000, // 最多1000条日志
    enableConsole: true, // 启用控制台输出
    enableStorage: false // 禁用存储日志
  },
  
  // 错误追踪
  errorTracking: {
    enabled: true,
    maxErrors: 100, // 最多记录100个错误
    reportCritical: true // 上报严重错误
  }
};

// 环境相关配置
const ENVIRONMENT_CONFIG = {
  development: {
    bluetooth: {
      ...BLUETOOTH_PERFORMANCE,
      dataQueryInterval: 1000 // 开发环境1秒查询
    },
    network: {
      ...NETWORK_PERFORMANCE,
      timeout: 30000 // 开发环境30秒超时
    },
    debug: {
      ...DEBUG_PERFORMANCE,
      logging: {
        ...DEBUG_PERFORMANCE.logging,
        level: 'debug',
        enableConsole: true,
        enableStorage: true
      }
    }
  },
  
  production: {
    bluetooth: BLUETOOTH_PERFORMANCE,
    network: NETWORK_PERFORMANCE,
    debug: {
      ...DEBUG_PERFORMANCE,
      logging: {
        ...DEBUG_PERFORMANCE.logging,
        level: 'warn',
        enableConsole: false,
        enableStorage: false
      }
    }
  }
};

// 获取当前环境配置
const getCurrentConfig = () => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  return isDevelopment ? ENVIRONMENT_CONFIG.development : ENVIRONMENT_CONFIG.production;
};

// 性能优化工具函数
const PerformanceUtils = {
  // 节流函数
  throttle(func, delay) {
    let timeoutId;
    let lastExecTime = 0;
    
    return function (...args) {
      const currentTime = Date.now();
      
      if (currentTime - lastExecTime > delay) {
        func.apply(this, args);
        lastExecTime = currentTime;
      } else {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          func.apply(this, args);
          lastExecTime = Date.now();
        }, delay - (currentTime - lastExecTime));
      }
    };
  },
  
  // 防抖函数
  debounce(func, delay) {
    let timeoutId;
    
    return function (...args) {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
  },
  
  // 批量更新
  batchUpdate(updates, delay = 100) {
    return new Promise((resolve) => {
      setTimeout(() => {
        updates.forEach(update => update());
        resolve();
      }, delay);
    });
  },
  
  // 内存清理
  cleanup() {
    // 清理缓存
    if (typeof wx !== 'undefined' && wx.clearStorage) {
      const storageInfo = wx.getStorageInfoSync();
      if (storageInfo.currentSize > MEMORY_MANAGEMENT.storage.maxSize * MEMORY_MANAGEMENT.storage.cleanupThreshold) {
        console.log('🧹 开始清理存储空间');
        // 这里可以添加具体的清理逻辑
      }
    }
  }
};

// uni-app兼容的导出方式
module.exports = {
  BLUETOOTH_PERFORMANCE,
  DATA_UPDATE_PERFORMANCE,
  NETWORK_PERFORMANCE,
  MEMORY_MANAGEMENT,
  UI_PERFORMANCE,
  DEBUG_PERFORMANCE,
  getCurrentConfig,
  PerformanceUtils,
  default: {
    bluetooth: BLUETOOTH_PERFORMANCE,
    dataUpdate: DATA_UPDATE_PERFORMANCE,
    network: NETWORK_PERFORMANCE,
    memory: MEMORY_MANAGEMENT,
    ui: UI_PERFORMANCE,
    debug: DEBUG_PERFORMANCE,
    getCurrentConfig,
    utils: PerformanceUtils
  }
};
