package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.AirUser;

/**
 * 小程序用户Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-14
 */
public interface IAirUserService 
{
    /**
     * 查询小程序用户
     * 
     * @param userId 小程序用户主键
     * @return 小程序用户
     */
    public AirUser selectAirUserByUserId(Long userId);

    /**
     * 根据手机号查询用户
     * 
     * @param phone 手机号
     * @return 小程序用户
     */
    public AirUser selectAirUserByPhone(String phone);

    /**
     * 根据openid查询用户
     * 
     * @param openid 微信openid
     * @return 小程序用户
     */
    public AirUser selectAirUserByOpenid(String openid);

    /**
     * 查询小程序用户列表
     * 
     * @param airUser 小程序用户
     * @return 小程序用户集合
     */
    public List<AirUser> selectAirUserList(AirUser airUser);

    /**
     * 新增小程序用户
     * 
     * @param airUser 小程序用户
     * @return 结果
     */
    public int insertAirUser(AirUser airUser);

    /**
     * 修改小程序用户
     * 
     * @param airUser 小程序用户
     * @return 结果
     */
    public int updateAirUser(AirUser airUser);

    /**
     * 批量删除小程序用户
     * 
     * @param userIds 需要删除的小程序用户主键集合
     * @return 结果
     */
    public int deleteAirUserByUserIds(Long[] userIds);

    /**
     * 删除小程序用户信息
     * 
     * @param userId 小程序用户主键
     * @return 结果
     */
    public int deleteAirUserByUserId(Long userId);

    /**
     * 微信登录
     * 
     * @param code 微信授权码
     * @return 用户信息
     */
    public AirUser wechatLogin(String code);

    /**
     * 手机号登录
     * 
     * @param phone 手机号
     * @param code 验证码
     * @return 用户信息
     */
    public AirUser phoneLogin(String phone, String code);

    /**
     * 注册用户
     * 
     * @param airUser 用户信息
     * @return 结果
     */
    public AirUser registerUser(AirUser airUser);

    /**
     * 更新用户信息
     * 
     * @param airUser 用户信息
     * @return 结果
     */
    public int updateUserInfo(AirUser airUser);
}
